import User from "../models/user.mjs";
import Group from "../models/group.mjs";
import Category from "../models/category.mjs"
import Contest from "../models/contest.mjs"
import Classification from "../models/classification.mjs"
import Invoice from "../models/invoice.mjs"
import Judge from "../models/judge.mjs"
import sgMail from '@sendgrid/mail'
import PDFDocument from 'pdfkit'
import fs from 'fs'
import path from 'path';
import { fileURLToPath } from 'url';

export default async function (fastify,opts){
    sgMail.setApiKey(process.env.SENDGRID_API_KEY)

    //add templateIds
    const templateIds = {
        confirmation: "d-865f6a897e7d4c1798cf6728be31eb82",
        instructions: "d-ddd8017ab34c477da7f6bc8e7f3fd255",
        updates: "d-your-template-id-for-updates",
        reminders: "d-your-template-id-for-reminders"
    };

    // FME company information used in invoice generation
    const fmeInfo = {
        address: {
            street: "39 Travis Park Dr",
            city: "Sugar Land",
            state: "TX",
            zip: "77479"
        },
        contactInfo: {
            email: "<EMAIL>",
            phone: "************"
        },
        note: "Checks to be made out to The Foundation for Music Education. Thank You."
    };

    function formatDate(date) {
        const day = date.getDate();
        const month = date.getMonth() + 1;
        const year = date.getFullYear();

        return month + "/" + day + "/" + year;
      }

    function formatCurrency(price) {
        return "$" + (price).toFixed(2);
      }

      function generateTableRow(
        doc,
        y,
        description,
        unitCost,
        quantity,
        lineTotal
      ) {
        const descriptionWidth = 200; // Adjust as needed
        const unitCostWidth = 90;
        const quantityWidth = 90;
        const lineTotalWidth = 90;
        const leftMargin = 50; // Adjust as needed
        const tableWidth = 550; // Adjust as needed
        const columnGap = 10; // Adjust as needed

        const descriptionX = leftMargin;
        const unitCostX = descriptionX + descriptionWidth + columnGap;
        const quantityX = unitCostX + unitCostWidth + columnGap;
        const lineTotalX = quantityX + quantityWidth + columnGap;

        doc
          .fontSize(10)
          .text(description, descriptionX, y, { width: descriptionWidth })
          .text(unitCost, unitCostX, y, { width: unitCostWidth, align: "right" })
          .text(quantity, quantityX, y, { width: quantityWidth, align: "right" })
          .text(lineTotal, lineTotalX, y, { width: lineTotalWidth, align: "right" });
      }

    function generateHr(doc, y) {
        doc
          .strokeColor("#aaaaaa")
          .lineWidth(1)
          .moveTo(50, y)
          .lineTo(550, y)
          .stroke();
    }

    function generateHeader(doc, fmeInfo) {
        const leftMargin = 50; // Adjust this value as needed

        doc
            .fillColor('#444444')
            .fontSize(10)
            .text("The Foundation for Music Education", leftMargin, 50, { align: "left" })
            .text(fmeInfo.address.street, leftMargin, 65, { align: "left" })
            .text(`${fmeInfo.address.city}, ${fmeInfo.address.state}, ${fmeInfo.address.zip}`, leftMargin, 80, { align: "left" })
            .text(`Email To: ${fmeInfo.contactInfo.email}`, leftMargin, 95, { align: "left" })
            .text(`Phone: ${fmeInfo.contactInfo.phone}`, leftMargin, 110, { align: "left" })
            .moveDown();
    }

    function generateCustomerInformation(doc, invoice) {
        doc
          .fillColor("#444444")
          .fontSize(20)
          .text("Invoice", 50, 160);

        generateHr(doc, 185);

        const customerInformationTop = 200;

        doc
          .fontSize(10)
          .text("Invoice Number:", 50, customerInformationTop)
          .font("Helvetica-Bold")
          .text(invoice.invoiceNo, 150, customerInformationTop)
          .font("Helvetica")
          .text("Invoice Date:", 50, customerInformationTop + 15)
          .text(formatDate(invoice.invoiceDate), 150, customerInformationTop + 15)
          .text("Invoice Due:", 50, customerInformationTop + 30)
          .text(
            formatDate(invoice.invoiceDue),
            150,
            customerInformationTop + 30
          )

          .font("Helvetica-Bold")
          .text(`Bill To: ${invoice.billTo}`, 300, customerInformationTop)
          .moveDown();
        generateHr(doc, 252);
    }

    function generateInvoiceTable(doc, invoice) {
        const invoiceTableTop = 330;
        const lineHeight = 30; // Height of each row
        const tableHeight = (invoice.lineItems.length + 2) * lineHeight; // Total height of the table including headers and subtotal/balance due

        doc.font("Helvetica-Bold");
        generateTableRow(
          doc,
          invoiceTableTop,
          "Description",
          "Unit Cost",
          "Quantity",
          "Total"
        );
        generateHr(doc, invoiceTableTop + 20);
        doc.font("Helvetica");

        for (let i = 0; i < invoice.lineItems.length; i++) {
          const item = invoice.lineItems[i];
          const position = invoiceTableTop + (i + 1) * lineHeight;
          generateTableRow(
            doc,
            position,
            item.description,
            formatCurrency(item.price),
            item.qty,
            formatCurrency(item.total)
          );

          generateHr(doc, position + 20);
        }

        // Calculate positions for subtotal and balance due based on the total height of the table
        let invoiceTotal = formatCurrency(invoice.total)
        const subtotalPosition = invoiceTableTop + tableHeight;
        generateTableRow(
          doc,
          subtotalPosition,
          "",
          "",
          `Subtotal: ${invoiceTotal} `,
          ""
        );

        const duePosition = subtotalPosition + lineHeight;
        doc.font("Helvetica-Bold");
        generateTableRow(
          doc,
          duePosition,
          "",
          "",
          `Balance Due: ${invoiceTotal} `,
          ""
        );
        doc.font("Helvetica");
      }

    function generateFooter(doc,note) {
        doc
          .fontSize(10)
          .text(
            note,
            50,
            780,
            { align: "center", width: 500 }
          );
    }

    async function generateInvoice(invoiceData){
        try {
            let coe = await Contest.findById("64345c8fdd98890aad8cef3d").lean()
            let moe = await Contest.findById("64345bd6dd98890aad8cef3c").lean()
            let lineItems = []
            let moeTotal = moe.price * invoiceData.moeCount
            let coeTotal = coe.price * invoiceData.coeCount
            let invoiceTotal = moeTotal + coeTotal
            if(invoiceData.coeCount > 0){
                lineItems.push({
                    qty: invoiceData.coeCount,
                    description: `${coe.name} registration fee`,
                    price: coe.price,
                    total: coeTotal
                })

            }
            if(invoiceData.moeCount > 0){
                lineItems.push({
                    qty: invoiceData.moeCount,
                    description: `${moe.name} registration fee`,
                    price: moe.price,
                    total: moeTotal
                })
            }

            // Create a new invoice object
            const newInvoice = new Invoice({
               billTo: invoiceData.billTo,
               lineItems: lineItems,
               total: invoiceTotal
            });

            // Save the new invoice to the database
            await newInvoice.save();
            console.log('Invoice created successfully:', newInvoice);
            let hmm = Group.find({_id:{$in:invoiceData.groupIds}}).lean()
            console.log('hmm')
            console.log(hmm)
            Group.updateMany(
                {_id:{$in:invoiceData.groupIds}},
                {$set:{invoice: newInvoice._id}}
            ).then(result => {
                console.log(`${result.nModified} groups updated.`);
              })
              .catch(error => {
                console.error('Error updating groups:', error);
              });
            let doc = new PDFDocument({ size: "A4", margin: 50 });
            generateHeader(doc,fmeInfo);
            generateCustomerInformation(doc, newInvoice);
            generateInvoiceTable(doc, newInvoice);
            generateFooter(doc,fmeInfo.note);

            doc.end();
            let path = `../tmp_invoice.pdf`
            const fileStream = fs.createWriteStream(path);
            doc.pipe(fileStream);
            await new Promise((resolve, reject) => {
                fileStream.on('finish', resolve);
                fileStream.on('error', reject);
            });
            return{
                invoiceNo: newInvoice.invoiceNo,
                path: path
            }

        } catch (error) {
            console.error('Error creating invoice:', error);
            throw error;
        }
    }

    async function sendMail(sendTo, useTemplate, templateData, invoicePDF){
        console.log(process.env.SENDGRID_API_KEY);
        const from = {email: '<EMAIL>',name:"The Foundation for Music Education"}
        let today = new Date();
        let mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!
        let dd = String(today.getDate()).padStart(2, '0');
        let yyyy = today.getFullYear();
        let formatedDate = mm + '-' + dd + '-' + yyyy;
        console.dir(templateData,{depth:null})
        let filename = `FME-Invoice_${invoicePDF.invoiceNo}.pdf`
        const dirname = path.dirname(fileURLToPath(import.meta.url));
        // Read the PDF file and encode its content to base64
        const pdfPath = path.resolve(dirname,'../', invoicePDF.path);
        const pdfContent = await fs.promises.readFile(pdfPath, { encoding: 'base64' });
        let cc = process.env.NODE_ENV === "dev" ? "<EMAIL>" : '<EMAIL>';
        const msg = {
            to: sendTo,
            cc: cc,
            from: from,
            replyTo: '<EMAIL>',
            templateId: useTemplate,
            dynamicTemplateData: templateData,
            attachments:[
                {
                    content: pdfContent,
                    filename: filename,
                    type: "application/pdf",
                    disposition:"attachment"
                }
            ]
          };

          // Send the email using the SendGrid client
          sgMail.send(msg)
            .then(() => {
              console.log('Email sent successfully');
            })
            .catch((error) => {
              console.error('Error sending email:', error);
              console.dir(error, {depth:null})
            });
    }

    fastify.post('/api/sendConfirmation', async function(request, response){
        console.log('send confirmation email');
        const {groups, email} = request.body
        const sendTo = email

        let invoiceData = {
            groupIds: [],
            moeCount: 0,
            coeCount: 0,
            billTo: groups[0].schoolName,
        }

        let templateData = {
            groups: await Promise.all(groups.map(async (group) => {
                let tracks = group.tracks.map((track, index) => ({
                    selection: index + 1,
                    title: track.title,
                    composer: track.composer
                }));
               invoiceData.groupIds.push(group._id)
                if(group.contest === "64345c8fdd98890aad8cef3d"){
                    invoiceData.coeCount += 1
                }else{
                    invoiceData.moeCount += 1
                }
                // Assume group.category and group.classification are populated fields
                let category = await Category.findById(group.category, "name -_id").lean()
                let classifications = [];

                if (Array.isArray(group.classification) && group.classification.length > 1) {
                    invoiceData.moeCount += 1
                    for (const classificationId of group.classification) {
                        const classification = await Classification.findById(classificationId,"name -_id").lean();
                        if (classification) {
                            classifications.push(classification.name);
                        }
                    }
                } else {
                    const classification = await Classification.findById(group.classification,"name -_id").lean();
                    if (classification) {
                        classifications.push(classification.name);
                    }
                }
                console.log(classifications)
                return {
                    schoolName: group.schoolName,
                    ensembleName: group.ensembleName,
                    director: group.director,
                    category: category.name,
                    classifications: classifications,
                    tracks: tracks
                };
            }))
        };
        let invoicePDF = await generateInvoice(invoiceData)
        console.log('sendCon', invoicePDF);
        sendMail(sendTo, templateIds.confirmation, templateData,invoicePDF );
        response.status(200)
    })

    fastify.post('/api/passwordReset:email', async function(request,response){
        console.log('send password reset email');
        const email = request.params.email
        response.status(200);
    })

    // Generate invoice PDF from an existing invoice ID
    async function generateInvoicePDF(invoiceId) {
        try {
            // Find the invoice by ID
            const invoice = await Invoice.findById(invoiceId);

            if (!invoice) {
                throw new Error('Invoice not found');
            }

            // Create PDF document
            let doc = new PDFDocument({ size: "A4", margin: 50 });
            generateHeader(doc, fmeInfo);
            generateCustomerInformation(doc, invoice);
            generateInvoiceTable(doc, invoice);
            generateFooter(doc, fmeInfo.note);

            doc.end();
            let path = `../tmp_invoice_${invoice.invoiceNo}.pdf`;
            const fileStream = fs.createWriteStream(path);
            doc.pipe(fileStream);

            await new Promise((resolve, reject) => {
                fileStream.on('finish', resolve);
                fileStream.on('error', reject);
            });

            return {
                invoiceNo: invoice.invoiceNo,
                path: path
            };
        } catch (error) {
            console.error('Error generating invoice PDF:', error);
            throw error;
        }
    }

    // Route to generate invoice PDF from an existing invoice ID
    fastify.get('/api/generateInvoice/:id', async function(request, reply) {
        try {
            const invoiceId = request.params.id;
            console.log(`Generating invoice PDF for invoice ID: ${invoiceId}`);

            const invoicePDF = await generateInvoicePDF(invoiceId);

            // Get the absolute path to the PDF file
            const dirname = path.dirname(fileURLToPath(import.meta.url));
            const pdfPath = path.resolve(dirname, '..', invoicePDF.path);

            // Read the PDF file
            const pdfContent = await fs.promises.readFile(pdfPath);

            // Set appropriate headers for PDF download
            reply.header('Content-Type', 'application/pdf');
            reply.header('Content-Disposition', `attachment; filename=FME-Invoice_${invoicePDF.invoiceNo}.pdf`);

            // Send the PDF file
            return reply.send(pdfContent);
        } catch (error) {
            console.error('Error in generateInvoice route:', error);
            return reply.status(500).send({ error: 'Failed to generate invoice PDF' });
        }
    });

    fastify.post('/api/resultsLive', async function(request,response){
        console.log('send resuls live email')
        const currentYear = (new Date()).getFullYear();

        const userList = await User.find({
            groups: {
                $exists: true,
                $not: { $size: 0 },
                $elemMatch: {
                    year: currentYear
                }
            }
        })
        .select('email')
        .exec()
        .then(users => users.map(user => user.email));
        console.log(userList)
        //send users that results are live and that they can view them
        //can be a single mass email as this doesnt need
        response.status(200);
    })

    fastify.post('/api/sendJudgeLinks',async function(request,response){
        console.log('send links to judges');
        const judgeList = await Judge.find({isActive: true})
            .select('email uri')
            .lean()
        console.log(judgeList)
        //send judges their link -- with instructions
        response.status(200);
    })

    fastify.post('/api/sendJudgeEmails', async function(request, response) {
        const { template } = request.body;

        try {
            const judges = await Judge.find({
                isActive: true,
                name: { $nin:["Rick Yancey", "Jacob Simpson"]} 
            })
                .select('email name _id')
                .lean();

            // console.log('Template ID being used:', templateIds[template]);
            // console.log('Template requested:', template);

            // console.log(judges)
            // response.status(200)
            const emails = judges.map(judge => ({
                to: process.env.NODE_ENV === "dev" ? "<EMAIL>" : judge.email,
                from: {
                    email: '<EMAIL>',
                    name: "The Foundation for Music Education",
                },
                replyTo: '<EMAIL>',
                templateId: templateIds[template],
                dynamicTemplateData: {
                    name: judge.name,
                    link: `https://app.foundationformusiceducation.org/judge?judgeId=${judge._id}`
                }
            }));
            // Send emails one at a time to better handle errors
            for (const email of emails) {
                try {
                    await sgMail.send(email);
                    console.log('Email sent successfully to:', email.to);
                } catch (error) {
                    console.error('Error sending email to', email.to, ':', error.response?.body);
                    throw error;
                }
            }

            response.status(200).send({ message: 'Emails sent successfully' });
        } catch (error) {
            console.error('Error sending emails:', error.response?.body || error);
            response.status(500).send({ error: 'Failed to send emails' });
        }
    });

}
