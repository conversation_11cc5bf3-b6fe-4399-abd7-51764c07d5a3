import mongoose from 'mongoose';
import * as <PERSON><PERSON> from 'minio';
import Judge from '../models/judge.mjs';
import Track from '../models/track.mjs';

// Usage examples:
//  - node server/scripts/listUnlinkedJudgeMemos.mjs --year=2025 --skipJudge='<PERSON>,<PERSON>'
//  - MONGO_URI=mongodb://mongo:27017/fme node server/scripts/listUnlinkedJudgeMemos.mjs --year=2025 --format=json
// Notes:
//  - Scans MinIO under {year}/judges/{Judge Name}/**
//  - For each object, derives trackId from folder: {year}/judges/{Judge Name}/{trackId}/{filename}
//  - If that object URL is NOT present in the Track.memos.audioMemos[].content array, it's considered unlinked and is output
//  - Default output is plain text (one URL per line). Use --format=json for structured output

function parseArgs() {
  const args = process.argv.slice(2);
  const out = {
    year: new Date().getFullYear().toString(),
    skipJudges: [],
    mongo: null,
    format: 'txt', // 'txt' | 'json'
  };
  for (const a of args) {
    if (a.startsWith('--year=')) out.year = a.split('=')[1];
    else if (a.startsWith('--mongo=')) out.mongo = a.split('=')[1];
    else if (a.startsWith('--skipJudge=')) {
      const names = a.split('=')[1] || '';
      out.skipJudges = names.split(',').map(s => s.trim()).filter(Boolean);
    }
    else if (a.startsWith('--format=')) out.format = a.split('=')[1];
  }
  return out;
}

const minioClient = new Minio.Client({
  endPoint: 'app.foundationformusiceducation.org',
  port: 443,
  useSSL: true,
  accessKey: 'fme-app-1',
  secretKey: 'D0fqEd5GCgBdGRhKiwNgAwbXRdkqIwRM1BtMmqWQ',
});
const BUCKET = 'fme-app';
const PUBLIC_BASE = 'https://app.foundationformusiceducation.org/fme-app/';

async function listMinioObjects(prefix) {
  return new Promise((resolve, reject) => {
    const objects = [];
    const stream = minioClient.listObjectsV2(BUCKET, prefix, true);
    stream.on('data', obj => objects.push(obj));
    stream.on('error', reject);
    stream.on('end', () => resolve(objects));
  });
}

function parseObjectPath(objName, year, judgeName) {
  // expected: `${year}/judges/${judgeName}/${trackId}/filename.ext`
  if (!objName.startsWith(`${year}/judges/${judgeName}/`)) return null;
  const parts = objName.split('/');
  if (parts.length < 5) return null; // year, judges, judgeName, trackId, filename
  const trackId = parts[3];
  const filename = parts.slice(4).join('/');
  return { trackId, filename };
}

async function groupObjectsByTrack(year, judgeName) {
  const prefix = `${year}/judges/${judgeName}/`;
  const objs = await listMinioObjects(prefix);
  const byTrack = new Map();
  for (const o of objs) {
    const parsed = parseObjectPath(o.name, year, judgeName);
    if (!parsed) continue;
    const { trackId } = parsed;
    const url = PUBLIC_BASE + o.name;
    if (!byTrack.has(trackId)) byTrack.set(trackId, []);
    byTrack.get(trackId).push(url);
  }
  return byTrack;
}

function existingContentSet(track) {
  const set = new Set();
  const memos = track?.memos?.audioMemos || [];
  for (const m of memos) {
    if (typeof m?.content === 'string') set.add(m.content);
  }
  return set;
}

async function main() {
  const { year, skipJudges, mongo, format } = parseArgs();
  console.error(`Listing unlinked judge memo files (year=${year})...`);
  if (skipJudges?.length) console.error(`Skipping judges: ${skipJudges.join(', ')}`);

  const mongoUri = process.env.MONGO_URI || mongo || 'mongodb://mongo:27017/fme';
  console.error(`Connecting to MongoDB at ${mongoUri} ...`);
  await mongoose.connect(mongoUri);
  mongoose.set('debug', false);

  const judges = await Judge.find({ isActive: { $ne: false } }).lean();

  const results = [];
  let totalObjects = 0;
  let totalUnlinked = 0;

  for (const judge of judges) {
    if (skipJudges.includes(judge.name)) continue;
    let byTrack;
    try {
      byTrack = await groupObjectsByTrack(year, judge.name);
    } catch (err) {
      console.error(`[ERROR] listing MinIO for judge '${judge.name}':`, err?.message || err);
      continue;
    }
    for (const [trackId, urls] of byTrack.entries()) {
      totalObjects += urls.length;
      const track = await Track.findById(trackId).lean();
      if (!track) {
        // No track: everything under this folder is unlinked by definition
        for (const url of urls) {
          results.push({ judge: judge.name, judgeId: judge._id.toString(), trackId, url, reason: 'track-not-found' });
          totalUnlinked++;
        }
        continue;
      }
      const contentSet = existingContentSet(track);
      for (const url of urls) {
        if (!contentSet.has(url)) {
          results.push({ judge: judge.name, judgeId: judge._id.toString(), trackId, url, reason: 'missing-in-track' });
          totalUnlinked++;
        }
      }
    }
  }

  // Output
  if (format === 'json') {
    console.log(JSON.stringify({ year, totalObjects, totalUnlinked, items: results }, null, 2));
  } else if (format === 'csv') {
    // CSV header
    console.log('judge,judgeId,trackId,url,reason');
    for (const r of results) {
      // naive CSV escaping for commas/dquotes
      const j = '"' + String(r.judge).replace(/"/g, '""') + '"';
      const jId = '"' + String(r.judgeId).replace(/"/g, '""') + '"';
      const t = '"' + String(r.trackId).replace(/"/g, '""') + '"';
      const u = '"' + String(r.url).replace(/"/g, '""') + '"';
      const rsn = '"' + String(r.reason).replace(/"/g, '""') + '"';
      console.log([j, jId, t, u, rsn].join(','));
    }
  } else {
    // Plain text: one URL per line
    for (const r of results) {
      console.log(r.url);
    }
    console.error(`\nSummary: total objects scanned=${totalObjects}, unlinked=${totalUnlinked}, year=${year}`);
  }

  await mongoose.disconnect();
}

main().catch(async (err) => {
  console.error('Fatal error:', err);
  try { await mongoose.disconnect(); } catch {}
  process.exit(1);
});

