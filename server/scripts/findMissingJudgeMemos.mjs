import mongoose from 'mongoose';
import * as Minio from 'minio';
import Group from '../models/group.mjs';
import Track from '../models/track.mjs';
import Judge from '../models/judge.mjs';

// Usage:
//   node server/scripts/findMissingJudgeMemos.mjs --year=2025 [--apply]
// Behavior:
//   - Finds, per track, which assigned judges (based on Judge.assignments) are missing an audio memo
//   - Looks for memo files in MinIO under: {year}/judges/{Judge Name}/{trackId}/
//   - In dry-run (default), prints a report. With --apply, inserts memo URLs into Track.memos.audioMemos

function parseArgs() {
  const args = process.argv.slice(2);
  const out = { apply: false, year: new Date().getFullYear().toString(), limit: null, mongo: null, skipJudges: [], mode: 'minio-first' };
  for (const a of args) {
    if (a === '--apply') out.apply = true;
    else if (a.startsWith('--year=')) out.year = a.split('=')[1];
    else if (a.startsWith('--limit=')) out.limit = parseInt(a.split('=')[1], 10) || null;
    else if (a.startsWith('--mongo=')) out.mongo = a.split('=')[1];
    else if (a.startsWith('--skipJudge=')) {
      const names = a.split('=')[1] || '';
      out.skipJudges = names.split(',').map(s => s.trim()).filter(Boolean);
    }
    else if (a.startsWith('--mode=')) out.mode = a.split('=')[1];
  }
  return out;
}

// MinIO client matches server/api/routes.mjs
const minioClient = new Minio.Client({
  endPoint: 'app.foundationformusiceducation.org',
  port: 443,
  useSSL: true,
  accessKey: 'fme-app-1',
  secretKey: 'D0fqEd5GCgBdGRhKiwNgAwbXRdkqIwRM1BtMmqWQ',
});
const BUCKET = 'fme-app';
const PUBLIC_BASE = 'https://app.foundationformusiceducation.org/fme-app/';

async function listMinioObjects(prefix) {
  return new Promise((resolve, reject) => {
    const objects = [];
    const stream = minioClient.listObjectsV2(BUCKET, prefix, true);
    stream.on('data', obj => objects.push(obj));
    stream.on('error', reject);
    stream.on('end', () => resolve(objects));
  });
}

function groupClassificationIds(group) {
  const ids = new Set();
  if (group.classification) ids.add(group.classification._id?.toString?.() || group.classification.toString());
  if (Array.isArray(group.classifications)) {
    for (const c of group.classifications) {
      if (c?.id) ids.add(c.id._id?.toString?.() || c.id.toString());
    }
  }
  return ids;
}

function isJudgeAssignedToGroup(judge, group) {
  // Judge assigned if any assignment matches group's category and classification/groups constraints
  const groupId = group._id.toString();
  const groupCat = (group.category?._id || group.category)?.toString?.();
  const groupClassIds = groupClassificationIds(group);
  if (!groupCat && groupClassIds.size === 0) return false;

  for (const assign of judge.assigned || []) {
    const assignCat = (assign.category?._id || assign.category)?.toString?.();
    if (!assignCat || assignCat !== groupCat) continue;
    for (const cls of assign.classifications || []) {
      // If specific groups restriction exists, require this group to be included
      if (Array.isArray(cls.groups) && cls.groups.length > 0) {
        const hasGroup = cls.groups.some(g => (g?._id || g).toString() === groupId);
        if (hasGroup) return true;
        continue; // groups restriction present but not matched
      }
      // Otherwise match by classification id
      const clsId = (cls.id?._id || cls.id)?.toString?.();
      if (clsId && groupClassIds.has(clsId)) return true;
    }
  }
  return false;
}

function memoUrlsOnTrackForJudge(track, judgeIdStr) {
  const urls = [];
  const memos = track?.memos?.audioMemos || [];
  for (const m of memos) {
    const mid = (m.judge?._id || m.judge)?.toString?.();
    if (mid === judgeIdStr && typeof m.content === 'string') {
      urls.push(m.content);
    }
  }
  return urls;
}

function getAssignedJudgesForGroup(group, judges, judgeById) {
  // Prefer explicit judge assignments from group's judgeScores if present
  const byScores = new Set();
  if (Array.isArray(group.judgeScores)) {
    for (const js of group.judgeScores) {
      if (js?.judgeId) byScores.add((js.judgeId?._id || js.judgeId).toString());
    }
  }
  if (Array.isArray(group.classifications)) {
    for (const cls of group.classifications) {
      if (Array.isArray(cls?.judgeScores)) {
        for (const js of cls.judgeScores) {
          if (js?.judgeId) byScores.add((js.judgeId?._id || js.judgeId).toString());
        }
      }
    }
  }
  if (byScores.size > 0) {
    const arr = [];
    for (const id of byScores) {
      const j = judgeById.get(id);
      if (j) arr.push(j);
      else console.warn(`[WARN] Judge id ${id} referenced in group ${group._id} not found`);
    }
    return arr;
  }
  // Fallback to Judge.assigned matching
  return judges.filter(j => isJudgeAssignedToGroup(j, group));
}

function buildJudgeNameIndex(judges) {
  const byName = new Map();
  for (const j of judges) {
    byName.set(j.name, j);
  }
  return byName;
}

function parseObjectForTrackId(objName, year, judgeName) {
  // expected: `${year}/judges/${judgeName}/${trackId}/filename.ext`
  if (!objName.startsWith(`${year}/judges/${judgeName}/`)) return null;
  const parts = objName.split('/');
  // parts[0]=year, [1]='judges', [2]=judgeName (may have spaces, but split keeps as one), [3]=trackId, [4...]=filename
  if (parts.length < 5) return null;
  return parts[3];
}

async function listJudgeObjectsGroupedByTrack(year, judgeName) {
  const prefix = `${year}/judges/${judgeName}/`;
  const objs = await listMinioObjects(prefix);
  const byTrack = new Map();
  for (const o of objs) {
    const trackId = parseObjectForTrackId(o.name, year, judgeName);
    if (!trackId) continue;
    const url = PUBLIC_BASE + o.name;
    if (!byTrack.has(trackId)) byTrack.set(trackId, []);
    byTrack.get(trackId).push(url);
  }
  return byTrack;
}

async function processMinioFirst({ year, apply, judges, skipJudges }) {
  const judgeByName = buildJudgeNameIndex(judges);
  let judgesScanned = 0;
  let trackFoldersSeen = 0;
  let trackUpdates = 0;

  for (const judge of judges) {
    if (skipJudges?.includes(judge.name)) continue;
    judgesScanned++;
    let byTrack;
    try {
      byTrack = await listJudgeObjectsGroupedByTrack(year, judge.name);
    } catch (err) {
      console.error(`[ERROR] listing MinIO for judge '${judge.name}':`, err?.message || err);
      continue;
    }
    if (byTrack.size === 0) continue;

    for (const [trackId, urls] of byTrack.entries()) {
      trackFoldersSeen++;
      const track = await Track.findById(trackId).lean();
      if (!track) {
        console.log(`[SKIP] Track not found for id=${trackId} (judge='${judge.name}')`);
        continue;
      }
      const existing = memoUrlsOnTrackForJudge(track, judge._id.toString());
      if (existing.length > 0) {
        // already has at least one memo for this judge; skip per requirements
        continue;
      }
      console.log(`[FOUND-MISSING] ${urls.length} file(s) in MinIO for judge='${judge.name}' track=${trackId} (no existing memos for this judge on track)`);
      if (apply) {
        const current = Array.isArray(track?.memos?.audioMemos) ? track.memos.audioMemos : [];
        const toAdd = urls.map(u => ({ judge: judge._id, content: u }));
        const newArray = current.concat(toAdd);
        await Track.updateOne({ _id: trackId }, { $set: { 'memos.audioMemos': newArray } });
        trackUpdates += toAdd.length;
        console.log(`  -> Added ${toAdd.length} memo URL(s) for judge='${judge.name}' to track=${trackId}`);
      }
    }
  }

  console.log('Summary (minio-first):');
  console.log(`  Judges scanned: ${judgesScanned}`);
  console.log(`  Judge track folders seen: ${trackFoldersSeen}`);
  if (apply) console.log(`  Memo URLs inserted: ${trackUpdates}`);
}

async function main() {
  const { year, apply, limit, mongo: mongoArg, skipJudges, mode } = parseArgs();
  console.log(`Scanning for missing judge audio memos (year=${year}, apply=${apply})...`);
  if (skipJudges?.length) console.log(`Skipping judges: ${skipJudges.join(', ')}`);

  const mongoUri = process.env.MONGO_URI || mongoArg || 'mongodb://mongo:27017/fme';
  console.log(`Connecting to MongoDB at ${mongoUri} ...`);
  await mongoose.connect(mongoUri);
  mongoose.set('debug', false);

  // Fetch all judges up-front
  const judges = await Judge.find({ isActive: { $ne: false } }).lean();
  const judgeById = new Map(judges.map(j => [j._id.toString(), j]));

  if (mode === 'minio-first') {
    await processMinioFirst({ year, apply, judges, skipJudges });
    await mongoose.disconnect();
    return;
  }

  // Fallback: scan by assignments (existing behavior)
  // Fetch groups with tracks populated; other refs are not required for matching
  let q = Group.find({}).populate([
    { path: 'tracks', select: 'memos title composer' }
  ]).lean();
  if (limit) q = q.limit(limit);
  const groups = await q;

  let totalTracks = 0;
  let totalAssignedPairs = 0;
  let fixed = 0;
  let missing = 0;

  for (const group of groups) {
    let assignedJudges = getAssignedJudgesForGroup(group, judges, judgeById);
    if (skipJudges?.length) {
      assignedJudges = assignedJudges.filter(j => !skipJudges.includes(j.name));
    }
    if (!assignedJudges.length) continue;

    for (const track of group.tracks || []) {
      totalTracks++;
      for (const judge of assignedJudges) {
        const judgeIdStr = judge._id.toString();
        const existing = memoUrlsOnTrackForJudge(track, judgeIdStr);
        totalAssignedPairs++;
        if (existing.length > 0) continue; // already has at least one memo

        // Missing memo: look in MinIO
        const prefix = `${year}/judges/${judge.name}/${track._id}/`;
        let objects = [];
        try {
          objects = await listMinioObjects(prefix);
        } catch (err) {
          console.error(`Error listing MinIO for prefix ${prefix}:`, err?.message || err);
          continue;
        }
        if (!objects.length) {
          missing++;
          console.log(`[MISSING] No memos found in MinIO for Group=${group._id} Track=${track._id} Judge='${judge.name}' prefix='${prefix}'`);
          continue;
        }

        const urls = objects.map(o => PUBLIC_BASE + o.name);
        console.log(`[FOUND] ${urls.length} memo(s) for Track=${track._id} Judge='${judge.name}'`);

        if (apply) {
          // Initialize memos container if needed
          if (!track.memos) track.memos = { audioMemos: [], notes: [], soloistNotes: [] };
          if (!Array.isArray(track.memos.audioMemos)) track.memos.audioMemos = [];
          const existingSet = new Set(memoUrlsOnTrackForJudge(track, judgeIdStr));
          let added = 0;
          for (const url of urls) {
            if (existingSet.has(url)) continue; // skip duplicates
            track.memos.audioMemos.push({ judge: judge._id, content: url });
            added++;
          }
          if (added > 0) {
            await Track.updateOne({ _id: track._id }, { $set: { 'memos.audioMemos': track.memos.audioMemos } });
            fixed += added;
            console.log(`  -> Inserted ${added} memo URL(s) on Track=${track._id}`);
          }
        }
      }
    }
  }

  console.log('Summary:');
  console.log(`  Groups scanned: ${groups.length}`);
  console.log(`  Tracks scanned: ${totalTracks}`);
  console.log(`  Judge-track pairs checked: ${totalAssignedPairs}`);
  console.log(`  Missing pairs (no objects in MinIO): ${missing}`);
  if (apply) console.log(`  URLs inserted: ${fixed}`);

  await mongoose.disconnect();
}

main().catch(async (err) => {
  console.error('Fatal error:', err);
  try { await mongoose.disconnect(); } catch {}
  process.exit(1);
});

