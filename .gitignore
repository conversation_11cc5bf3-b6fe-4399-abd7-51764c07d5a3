# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependenciesgit 
/.pnp
.pnp.js
node_modules

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

data/db/mongo_data
data/db/mongo_data
sendgrid.env

docker-compose.yml
server/unlinked_2025.csv
csvs/unlinked_2025.csv
app/src/lib/tableHelper.test.js
app/src/Components/JudgeScoresTest.jsx
