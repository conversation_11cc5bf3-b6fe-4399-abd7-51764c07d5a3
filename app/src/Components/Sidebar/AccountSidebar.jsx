import classNames from "classnames";
import { useContext, useState } from "react";
import { observer } from "mobx-react";
import { AppProviderStore } from "./../../AppStore";
import { Link } from "react-router-dom";

const AccountSidebar = ({ toggleRegistrationView, showRegistration }) => {
  const { AppStore } = useContext(AppProviderStore);
  const { selectedGroup, setGroup, groups, competitionState } = AppStore;

  const [showPastGroups, setShowPastGroups] = useState(false);
  const currentYear = new Date().getFullYear();

  // Filter out archived groups and then filter by year
  const filteredGroups = groups.filter(
    (group) =>
      !group.group.isArchived &&
      (showPastGroups
        ? group.group.year < currentYear
        : group.group.year === currentYear)
  );

  return (
    <div className="w-[270px] pt-10 min-h-full bg-sidebar relative">
      {/* Toggle <PERSON> (styled like other buttons) */}
      <div className="flex justify-center mb-4">
        <button
          onClick={() => setShowPastGroups(!showPastGroups)}
          className="p-3 bg-iconGreen text-white font-semibold rounded-lg hover:bg-[#3b8c6e] transition"
        >
          {showPastGroups ? "Show Current Groups" : "Show Past Groups"}
        </button>
      </div>

      <ul className="flex flex-col gap-5">
        {filteredGroups.map((data) => (
          <li
            key={data.group._id}
            onClick={() => {
              setGroup(data);
              showRegistration && toggleRegistrationView();
            }}
            className={classNames(
              "group flex items-center w-11/12 gap-2 font-semibold p-5 pr-0 duration-300",
              "hover:bg-[#3b8c6e] hover:text-white hover:w-[100%]",
              selectedGroup?._id === data.group._id
                ? "bg-sidebarListItem text-white w-[100%]"
                : "bg-white text-grayText"
            )}
          >
            {/* Add year to past groups */}
            {showPastGroups
              ? `${data.group.ensembleName} (${data.group.year})`
              : data.group.ensembleName}
          </li>
        ))}
      </ul>

      {/* Conditional rendering of the "Register Group" button */}
      {competitionState.registrationState && !showPastGroups && (
        <button
          className="absolute bottom-20 left-1/2 transform -translate-x-1/2 p-3 bg-iconGreen text-white font-semibold rounded-lg hover:bg-[#3b8c6e] transition"
          onClick={toggleRegistrationView}
        >
          Register Group
        </button>
      )}
      {competitionState.resultState && (
        <Link to="/results">
          <button className="absolute bottom-20 left-1/2 transform -translate-x-1/2 p-3 bg-iconGreen text-white font-semibold rounded-lg hover:bg-[#3b8c6e] transition">
            View Winners
          </button>
        </Link>
      )}
      
      {/* Support Button */}
      <a 
        href="mailto:<EMAIL>,<EMAIL>"
        className="absolute bottom-5 left-1/2 transform -translate-x-1/2 p-3 bg-iconGreen text-white font-semibold rounded-lg hover:bg-[#3b8c6e] transition"
      >
        Support
      </a>
    </div>
  );
};

export default observer(AccountSidebar);
