import { useContext, useState, useEffect, useMemo } from "react";
import { observer } from "mobx-react";
import { AppProviderStore } from "./../../AppStore";
import {
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  IconButton,
  Typography,
  Box,
  Divider,
  Pagination,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
} from "@mui/material";
import {
  CheckCircle as CheckCircleIcon,
  ArrowBack as ArrowBackIcon,
  ViewCompact as ViewCompactIcon,
  ViewList as ViewListIcon,
} from "@mui/icons-material";

const JudgeSidebar = ({ handleSelect, assignments }) => {
  const { AppStore } = useContext(AppProviderStore);
  const [currentView, setCurrentView] = useState('categories');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedClassification, setSelectedClassification] = useState(null);

  // Pagination and view state
  const [currentPage, setCurrentPage] = useState(1);
  const [isCompactView, setIsCompactView] = useState(false);
  const itemsPerPage = isCompactView ? 15 : 10;

  // Update sidebar state when AppStore.selectedGroup changes
  useEffect(() => {
    // Only proceed if there's a selected group in AppStore
    if (AppStore.selectedGroup && AppStore.selectedGroup._id) {
      console.log('JudgeSidebar: AppStore.selectedGroup changed', AppStore.selectedGroup._id);

      // 1) Prefer the currently selected classification, if it contains this group
      const currentCls = AppStore.currentSelectedClassification;
      if (currentCls && assignments && assignments.length) {
        const wantedId = typeof currentCls.id === 'string' ? currentCls.id : currentCls.id?._id;
        if (wantedId) {
          for (const assignment of assignments) {
            const matching = assignment.classifications.find(c => {
              const sameId = (typeof c.id === 'string' && c.id === wantedId) || (typeof c.id === 'object' && c.id?._id === wantedId);
              if (!sameId) return false;
              // Ensure this classification actually contains the selected group
              return Array.isArray(c.groups) && c.groups.some(g => g._id === AppStore.selectedGroup._id);
            });
            if (matching) {
              console.log('JudgeSidebar: Using currentSelectedClassification context', matching.id?.name || matching.id);
              setSelectedCategory(assignment);
              setSelectedClassification(matching);
              AppStore.setCurrentSelectedCategory(assignment);
              AppStore.setCurrentSelectedClassification(matching);
              setCurrentView('groups');
              return; // Respect the existing context
            }
          }
        }
      }

      // 2) Fallback: find the first classification that contains this group
      for (const assignment of assignments) {
        for (const classification of assignment.classifications) {
          const groupIndex = classification.groups.findIndex(g => g._id === AppStore.selectedGroup._id);
          if (groupIndex !== -1) {
            // Found the group in this classification
            console.log('JudgeSidebar: Found matching group in classification (fallback)', classification.id.name);
            setSelectedCategory(assignment);
            setSelectedClassification(classification);
            AppStore.setCurrentSelectedCategory(assignment);
            AppStore.setCurrentSelectedClassification(classification);
            setCurrentView('groups');
            return; // Exit once we've found and set the state
          }
        }
      }
      console.log('JudgeSidebar: Could not find matching group in any classification');
    }
  }, [AppStore.selectedGroup, assignments, AppStore.currentSelectedClassification]);

  // Add this function to handle group selection
  const handleGroupSelection = async (group, classificationId) => {
    if (AppStore.isRecording) {
      alert('Please stop recording before switching groups');
      return;
    }

    if (AppStore.isSavingMemo) {
      alert('Please wait for the memo to finish saving before switching groups');
      return;
    }

    // Save current group's score before switching
    if (AppStore.selectedGroup && AppStore.judgeScore) {
      await AppStore.setGroupScore();
    }

    try {
      // Fetch the latest group data from the server to ensure we have the most up-to-date information
      const response = await fetch(`/api/groups/${group._id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch updated group data');
      }

      // Update the group with fresh data from the server
      const updatedGroup = await response.json();
      console.log('Fetched fresh group data:', updatedGroup);

      // Update the group in the groups array to ensure sidebar displays correct data
      const groupIndex = selectedClassification.groups.findIndex(g => g._id === updatedGroup._id);
      if (groupIndex !== -1) {
        selectedClassification.groups[groupIndex] = updatedGroup;
      }

      // Use the updated group data for the rest of the function
      group = updatedGroup;
    } catch (error) {
      console.error('Error fetching updated group data:', error);
      // Continue with existing group data if fetch fails
    }

    let judgeScore;
    let classificationIndex = -1;

    if (!group.classifications?.length) {
      judgeScore = group.judgeScores?.find(score => score.judgeId === AppStore.judge._id);
    } else {
      // Handle both cases: when obj.id is a string (ObjectId) or populated object
      classificationIndex = group.classifications.findIndex(obj => {
        if (typeof obj.id === 'string') {
          return obj.id === classificationId;
        } else if (typeof obj.id === 'object' && obj.id._id) {
          return obj.id._id === classificationId;
        }
        return false;
      });

      console.log(`Found classification index: ${classificationIndex} for classificationId: ${classificationId}`);

      if (classificationIndex !== -1 && group.classifications[classificationIndex]?.judgeScores) {
        judgeScore = group.classifications[classificationIndex].judgeScores.find(score => score.judgeId === AppStore.judge._id);
        console.log(`Found existing judge score in classification ${classificationIndex}:`, judgeScore);
      }
    }

    // Set the selected classification index in AppStore
    AppStore.selectedClassificationIndex = classificationIndex;

    // Set default score if none exists
    const scoreToSet = judgeScore || {
      judgeId: AppStore.judge._id,
      score: null,
      movesOn: false,
      isCommended: false,
      isNational: false,
      isCitation: false,
      isState: false
    };

    // First set the group to ensure proper initialization
    AppStore.setGroup(group);

    // Call the handleSelect prop if it exists
    if (handleSelect && typeof handleSelect === 'function') {
      handleSelect(group);
    }

    // Then set the judge score
    AppStore.setJudgeScore(scoreToSet);
  };

  const getScore = (group, classificationId) => {
    let judgeScore;
    let inClassifications = -1;

    // Handle case where group has no classifications
    if (!group.classifications || !group.classifications.length) {
      if (group?.judgeScores && Array.isArray(group.judgeScores)) {
        judgeScore = group.judgeScores.find(score => score.judgeId === AppStore.judge._id);
      }
    } else {
      // Find the classification index - handle both cases: when obj.id is a string (ObjectId) or populated object
      let index = group.classifications.findIndex(obj => {
        if (typeof obj.id === 'string') {
          return obj.id === classificationId;
        } else if (typeof obj.id === 'object' && obj.id._id) {
          return obj.id._id === classificationId;
        }
        return false;
      });
      inClassifications = index;

      // Only proceed if we found a valid classification
      if (index !== -1 && group.classifications[index]) {
        // Make sure judgeScores is an array before trying to find a score
        if (group.classifications[index].judgeScores && Array.isArray(group.classifications[index].judgeScores)) {
          judgeScore = group.classifications[index].judgeScores.find(score => score.judgeId === AppStore.judge._id);
        }
      }
    }
    let defaultJudgeScore = {
      judgeId: AppStore.judge._id,
      score: null,
      movesOn: false,
      isCommended: false,
      isNational: false,
      isCitation: false,
      isState: false
    };
    if (inClassifications !== -1) {
      if (judgeScore) {
        judgeScore.classificationIndex = inClassifications;
      } else {
        defaultJudgeScore.classificationIndex = inClassifications;
      }
    }
    return judgeScore ? judgeScore : defaultJudgeScore;
  };

  const BackButton = ({ onClick, text }) => (
    <Box sx={{ p: 1 }}>
      <IconButton
        onClick={onClick}
        sx={{
          color: '#ffffff',
          padding: '4px',
          '&:hover': {
            color: '#3b8c6e',
          },
        }}
      >
        <ArrowBackIcon />
        <Typography
          variant="button"
          sx={{
            ml: 1,
            fontSize: '0.875rem'
          }}
        >
          {text}
        </Typography>
      </IconButton>
    </Box>
  );

  const renderCategories = () => (
    <List sx={{ width: '100%', p: 1 }}>
      {assignments.map((assignment) => (
        <ListItem
          key={assignment._id}
          disablePadding
          sx={{ mb: 1 }}
        >
          <ListItemButton
            onClick={() => {
              setSelectedCategory(assignment);
              AppStore.setCurrentSelectedCategory(assignment);
              setCurrentView('classifications');
            }}
            sx={{
              bgcolor: '#008544', // Changed from #3b8c6e
              borderRadius: 1,
              color: '#ffffff',
              '&:hover': {
                bgcolor: '#3b8c6e', // Changed from #008544
              },
            }}
          >
            <ListItemText primary={assignment.category.name} />
          </ListItemButton>
        </ListItem>
      ))}
    </List>
  );

  // Helper function to format classification name with phase
  const formatClassificationName = (classification) => {
    const baseName = classification.id.name;
    if (classification.phase) {
      return `${baseName} - Phase ${classification.phase}`;
    }
    return baseName;
  };

  const renderClassifications = () => (
    <>
      <BackButton
        onClick={() => {
          setCurrentView('categories');
          setSelectedCategory(null);
          AppStore.setCurrentSelectedCategory(null);
        }}
        text="Back to Categories"
      />
      <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.12)', my: 0.5 }} />
      <List sx={{ width: '100%', p: 1 }}>
        {selectedCategory.classifications
          .filter(classification => classification.groups && classification.groups.length > 0 || classification.phase !== undefined)
          .map((classification) => (
            <ListItem
              key={classification._id}
              disablePadding
              sx={{ mb: 1 }}
            >
              <ListItemButton
                onClick={() => {
                  setSelectedClassification(classification);
                  AppStore.setCurrentSelectedClassification(classification);
                  setCurrentView('groups');
                }}
                sx={{
                  bgcolor: '#008544', // Changed from #3b8c6e
                  borderRadius: 1,
                  color: '#ffffff',
                  '&:hover': {
                    bgcolor: '#3b8c6e', // Changed from #008544
                  },
                }}
              >
                <ListItemText primary={formatClassificationName(classification)} />
              </ListItemButton>
            </ListItem>
          ))}
      </List>
    </>
  );

  // Memoized pagination logic
  const paginatedGroups = useMemo(() => {
    if (!selectedClassification?.groups) return [];

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return selectedClassification.groups.slice(startIndex, endIndex);
  }, [selectedClassification?.groups, currentPage, itemsPerPage]);

  const totalPages = useMemo(() => {
    if (!selectedClassification?.groups) return 0;
    return Math.ceil(selectedClassification.groups.length / itemsPerPage);
  }, [selectedClassification?.groups?.length, itemsPerPage]);

  // Reset to page 1 when classification changes
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedClassification]);

  const handlePageChange = (_, value) => {
    setCurrentPage(value);
  };

  const handleViewModeChange = (_, newViewMode) => {
    if (newViewMode !== null) {
      setIsCompactView(newViewMode === 'compact');
      setCurrentPage(1); // Reset to first page when changing view mode
    }
  };

  const renderGroups = () => (
    <>
      <BackButton
        onClick={() => {
          if (AppStore.isRecording) {
            alert('Please stop recording before navigating away');
            return;
          }

          if (AppStore.isSavingMemo) {
            alert('Please wait for the memo to finish saving before navigating away');
            return;
          }
          // Deselect the group in AppStore
          console.log('JudgeSidebar: Deselecting group when going back to classifications');
          AppStore.unselectGroup();

          // Update local state
          setCurrentView('classifications');
          setSelectedClassification(null);
          AppStore.setCurrentSelectedClassification(null);
          setCurrentPage(1); // Reset pagination
        }}
        text="Back to Classifications"
      />
      <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.12)', my: 0.5 }} />

      {/* View Mode Toggle and Group Count */}
      <Box sx={{ p: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            {selectedClassification.groups.length} groups
          </Typography>

          <ToggleButtonGroup
            value={isCompactView ? 'compact' : 'normal'}
            exclusive
            onChange={handleViewModeChange}
            size="small"
            sx={{
              '& .MuiToggleButton-root': {
                color: 'rgba(255, 255, 255, 0.7)',
                borderColor: 'rgba(255, 255, 255, 0.3)',
                '&.Mui-selected': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                },
              },
            }}
          >
            <ToggleButton value="normal" size="small">
              <Tooltip title="Normal view">
                <ViewListIcon fontSize="small" />
              </Tooltip>
            </ToggleButton>
            <ToggleButton value="compact" size="small">
              <Tooltip title="Compact view">
                <ViewCompactIcon fontSize="small" />
              </Tooltip>
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>

        {/* Pagination info */}
        {totalPages > 1 && (
          <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.7)', textAlign: 'center' }}>
            Page {currentPage} of {totalPages}
          </Typography>
        )}
      </Box>

      <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.12)', my: 0.5 }} />

      <List sx={{ width: '100%', p: 1, minHeight: isCompactView ? '300px' : '400px' }}>
        {paginatedGroups.map((group) => {
          const score = getScore(group, selectedClassification.id._id);
          const isSelected = AppStore.selectedGroup?._id === group._id;

          // Create the list item text to display
          const maskedName = group.maskedName ||
            (group.classifications && group.classifications.find(
              (obj) => obj && (
                // Handle both cases: when obj.id is a string (ObjectId) or populated object
                (typeof obj.id === 'string' && obj.id === selectedClassification.id._id) ||
                (typeof obj.id === 'object' && obj.id._id === selectedClassification.id._id)
              )
            )?.maskedName) || '';
          const listItemText = `Group: ${maskedName}`;

          return (
            <ListItem
              key={group._id}
              disablePadding
              sx={{ mb: isCompactView ? 0.5 : 1 }}
            >
              <ListItemButton
                selected={isSelected}
                onClick={() => handleGroupSelection(group, selectedClassification.id._id)}
                disabled={AppStore.isRecording || AppStore.isSavingMemo}
                sx={{
                  bgcolor: isSelected ? '#3b8c6e' : '#ffffff',
                  color: isSelected ? '#ffffff' : '#000000',
                  borderRadius: 1,
                  py: isCompactView ? 0.5 : 1,
                  minHeight: isCompactView ? 40 : 56,
                  '&:hover': {
                    bgcolor: '#3b8c6e',
                    color: '#ffffff',
                    // Update icon color on hover
                    '& .MuiSvgIcon-root': {
                      color: '#ffffff !important',
                    },
                    // Add styles for disabled state
                    ...((AppStore.isRecording || AppStore.isSavingMemo) && {
                      bgcolor: '#ffffff',
                      cursor: 'not-allowed',
                      opacity: 0.5,
                      '& .MuiSvgIcon-root': {
                        color: '#3b8c6e !important',
                      },
                    })
                  },
                  '&.Mui-selected': {
                    bgcolor: '#3b8c6e',
                    '&:hover': {
                      bgcolor: '#3b8c6e',
                    },
                  },
                  // Add styles for disabled state
                  ...(AppStore.isRecording && {
                    opacity: 0.5,
                    cursor: 'not-allowed'
                  })
                }}
              >
                {score.score > 0 && (
                  <ListItemIcon sx={{ minWidth: isCompactView ? 28 : 36 }}>
                    <CheckCircleIcon
                      sx={{
                        color: isSelected ? '#ffffff' : '#3b8c6e',
                        fontSize: isCompactView ? '1rem' : '1.25rem'
                      }}
                    />
                  </ListItemIcon>
                )}
                <ListItemText
                  primary={listItemText}
                  secondary={!isCompactView && score.score ? `Score: ${score.score}` : undefined}
                  primaryTypographyProps={{
                    sx: {
                      fontSize: isCompactView ? '0.875rem' : '1rem',
                      lineHeight: isCompactView ? 1.2 : 1.5
                    }
                  }}
                  secondaryTypographyProps={{
                    sx: {
                      color: isSelected ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                      fontSize: '0.75rem'
                    }
                  }}
                />
                {/* Show score in compact view as a chip */}
                {isCompactView && score.score > 0 && (
                  <Box
                    sx={{
                      backgroundColor: isSelected ? 'rgba(255, 255, 255, 0.2)' : 'rgba(59, 140, 110, 0.1)',
                      color: isSelected ? '#ffffff' : '#3b8c6e',
                      borderRadius: '12px',
                      px: 1,
                      py: 0.25,
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      minWidth: '32px',
                      textAlign: 'center'
                    }}
                  >
                    {score.score}
                  </Box>
                )}
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <Box sx={{ p: 1, display: 'flex', justifyContent: 'center' }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            size="small"
            sx={{
              '& .MuiPaginationItem-root': {
                color: 'rgba(255, 255, 255, 0.7)',
                borderColor: 'rgba(255, 255, 255, 0.3)',
                '&.Mui-selected': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                },
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              },
            }}
          />
        </Box>
      )}
    </>
  );

  return (
    <Box
      sx={{
        width: 270,
        minHeight: '100%',
        bgcolor: '#0d3d75', // Changed back to previous blue
        pt: 2,
        overflow: 'auto',
      }}
    >
      {currentView === 'categories' && renderCategories()}
      {currentView === 'classifications' && renderClassifications()}
      {currentView === 'groups' && renderGroups()}
    </Box>
  );
};

export default observer(JudgeSidebar);
