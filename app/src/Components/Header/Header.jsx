/**
 * Pure Function, Do not add side effects
 */

import { observer } from 'mobx-react';
import { useState, useContext, useEffect } from 'react';
import { AppProviderStore } from './../../AppStore'
import YoboMobxDevTools from '../TestComponents/YoboMobxDevtools';
import { FaUserCircle } from "react-icons/fa";
import gravatarUrl from 'gravatar-url';
import { toJS } from "mobx"
import { useNavigate, useHistory, Link } from 'react-router-dom'
import UserProfileModal from '../UserProfileModal/UserProfileModal';
import JudgeIntroVideo from '../JudgeIntroVideo/JudgeIntroVideo';
import {
  Menu,
  MenuItem,
  IconButton,
  Tooltip,
  Box,
  Typography
} from '@mui/material';
import {
  VideoCall as VideoIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';

const AppHeader = () => {
  const { AppStore } = useContext(AppProviderStore);
  const navigate = useNavigate();
  const [isAdmin, setIsAdmin] = useState(false);
  const [showAdmin, setShowAdmin] = useState(false);
  const [isUser, setIsUser] = useState(false);
  const [isJudge, setIsJudge] = useState(false);
  const [name, setName] = useState();
  const [profileModalOpen, setProfileModalOpen] = useState(false);
  const [judgeMenuAnchor, setJudgeMenuAnchor] = useState(null);
  const judgeMenuOpen = Boolean(judgeMenuAnchor);

  useEffect(() => {
    console.log('use effect')
  }, [showAdmin])
  const handleLogout = () => {
    console.log('clicked logout')
    localStorage.clear();
    AppStore.reset();
    navigate('/')
  };

  const handleDashboardClick = () => {
    AppStore?.toggleShowAdmin();
    if (AppStore?.showAdmin) {
      navigate("/admin");
    } else {
      navigate("/account");
    }
  };

  const handleProfileClick = () => {
    console.log('Opening user profile modal');
    setProfileModalOpen(true);
  };

  const handleJudgeMenuClick = (event) => {
    setJudgeMenuAnchor(event.currentTarget);
  };

  const handleJudgeMenuClose = () => {
    setJudgeMenuAnchor(null);
  };

  const handleEditIntro = () => {
    AppStore.setJudgeVideoCreated(false);
    AppStore.setIsBanner(false);
    handleJudgeMenuClose();
  };

  useEffect(() => {
    console.log('location', window.location.href)
    if (AppStore?.judge) {
      setIsJudge(true)
      setName(AppStore.judge.name)
    } else if (AppStore?.user) {
      let username = AppStore.user?.name ? `${AppStore.user.name.first} ${AppStore.user.name.last}` : AppStore.user.email
      setName(username)
      if (AppStore?.user?.isAdmin) {
        // if(AppStore.user.email !== "<EMAIL>"){
        //   setShowAdmin(true)
        //   navigate("/admin")
        // }
        setIsAdmin(true)
      } else {
        setIsUser(true)
      }
    }

  }, [AppStore.user])


  /*   useEffect(() => {
      if (showAdmin) {
        console.log('is admin', showAdmin)
        navigate("/admin");
      } else {
        console.log('is admin', showAdmin)
        navigate("/account");
      }
    },[showAdmin]); */

  return (
    <>
      <div className="bg-header items-center fixed w-full h-[90px] p-5 flex justify-between z-10">
        <h4 className="text-white">
          The Foundation&nbsp;
          <span className="font-medium text-green">For Music Education</span>
        </h4>
        {isJudge ?
          (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Judge Menu">
                <Box
                  onClick={handleJudgeMenuClick}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    cursor: 'pointer',
                    padding: '8px 12px',
                    borderRadius: '8px',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    }
                  }}
                >
                  <img
                    src={AppStore.judge_profile_img}
                    alt="judge"
                    style={{
                      width: '30px',
                      height: '30px',
                      borderRadius: '50%'
                    }}
                  />
                  <Typography variant="h6" sx={{ color: 'white', fontWeight: 600 }}>
                    {name}
                  </Typography>
                  <ExpandMoreIcon sx={{ color: 'white', fontSize: '20px' }} />
                </Box>
              </Tooltip>

              <Menu
                anchorEl={judgeMenuAnchor}
                open={judgeMenuOpen}
                onClose={handleJudgeMenuClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
              >
                <MenuItem onClick={handleEditIntro}>
                  <VideoIcon sx={{ mr: 1 }} />
                  Edit Introduction Video
                </MenuItem>
              </Menu>
            </Box>
          ) : (
            <div className="flex items-center gap-3 font-semibold text-white">
              <h4 onClick={handleProfileClick} className="cursor-pointer">{name}</h4>
              {isAdmin && <button onClick={() => handleDashboardClick()}>{AppStore.showAdmin ? `View User Dashboard` : `View Admin Dashboard`} </button>}
              {AppStore.user ? <button onClick={handleLogout}>Logout</button> :
                <Link to="/login">
                  <button onClick={handleLogout}>Login</button>
                </Link>
              }
            </div>
          )
        }
      </div>
      {AppStore.user && (
        <UserProfileModal
          open={profileModalOpen}
          handleClose={() => setProfileModalOpen(false)}
        />
      )}
    </>
  );
};
export default observer(AppHeader);
