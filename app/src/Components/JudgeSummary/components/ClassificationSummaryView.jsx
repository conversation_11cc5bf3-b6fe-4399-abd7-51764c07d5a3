import React from 'react';
import {
  Box,
  Typography,
  Button,
  Paper
} from '@mui/material';
import {
  Class as ClassIcon,
  Group as GroupIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';
import BackButton from './BackButton';
import ScoringProgressCard from './ScoringProgressCard';
import TopGroupsSection from './TopGroupsSection';
import FinalAwardsSection from './FinalAwardsSection';
import GroupsTable from './GroupsTable';

const ClassificationSummaryView = ({
  selectedClassification,
  processedGroups,
  isAutoSelectLoading,
  handleAutoSelectTopGroups,
  handleMovesOnChange,
  handleNationalChange,
  handleCitationChange,
  handleCommendedChange,
  handleAutoSelectNational,
  handleAutoSelectCitation,
  handleAutoSelectCommended,
  handleGroupSelect,
  onBackToSummary,
  onViewAllGroups,
  isCitationCategory = false
}) => {
  if (!selectedClassification || !selectedClassification.groups) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">
          No groups found in this classification.
        </Typography>
      </Paper>
    );
  }

  // Helper function to format classification name with phase
  const formatClassificationName = (classification) => {
    const baseName = classification.id?.name || 'Classification';
    if (classification.phase) {
      return `${baseName} - Phase ${classification.phase}`;
    }
    return baseName;
  };

  // Calculate scoring statistics
  const totalGroups = processedGroups.length;
  const scoredGroups = processedGroups.filter(group => group.hasScore).length;
  const movesOnGroups = processedGroups.filter(group => group.movesOn).length;
  const nationalGroups = processedGroups.filter(group => group.isNational).length;
  const commendedGroups = processedGroups.filter(group => group.isCommended).length;
  const progress = totalGroups > 0 ? (scoredGroups / totalGroups) * 100 : 0;

  // Determine judge phase flags
  const phaseVal = selectedClassification?.phase;
  const isPhase2Judge = phaseVal === 2 || String(phaseVal) === '2';
  const isNoPhaseJudge = !phaseVal || phaseVal === 'Not specified' || String(phaseVal).trim() === '';
  const showFinalAwards = isPhase2Judge || isNoPhaseJudge;

  return (
    <Box sx={{ p: 3 }}>
      <BackButton
        onClick={onBackToSummary}
        text="Back to Summary"
      />

      <Typography variant="h5" sx={{ mb: 3, color: '#3b8c6e', display: 'flex', alignItems: 'center' }}>
        <ClassIcon sx={{ mr: 1 }} />
        {formatClassificationName(selectedClassification)} Summary
      </Typography>

      {/* Classification Summary Card */}
      <ScoringProgressCard 
        totalGroups={totalGroups}
        scoredGroups={scoredGroups}
        progress={progress}
        phase={selectedClassification.phase}
        movesOnGroups={movesOnGroups}
      />

      {/* Top 50% Groups Section - Only show for Phase 1 */}
      {selectedClassification.phase === 1 && (
        <TopGroupsSection
          sortedGroups={processedGroups}
          isAutoSelectLoading={isAutoSelectLoading}
          handleAutoSelectTopGroups={handleAutoSelectTopGroups}
          handleMovesOnChange={handleMovesOnChange}
        />
      )}

      {/* Final Awards Section - Show for Phase 2 judges or judges with no phase */}
      {showFinalAwards && (
        <FinalAwardsSection
          sortedGroups={processedGroups}
          isAutoSelectLoading={isAutoSelectLoading}
          handleNationalChange={handleNationalChange}
          handleCitationChange={handleCitationChange}
          handleCommendedChange={handleCommendedChange}
          handleAutoSelectNational={handleAutoSelectNational}
          handleAutoSelectCitation={handleAutoSelectCitation}
          handleAutoSelectCommended={handleAutoSelectCommended}
          isCitationCategory={isCitationCategory}
          isPhase2Judge={isPhase2Judge}
        />
      )}

      {/* Group List Summary */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" sx={{ color: '#3b8c6e', display: 'flex', alignItems: 'center' }}>
            <GroupIcon sx={{ mr: 1 }} /> Groups Overview
          </Typography>

          <Button
            variant="contained"
            color="primary"
            onClick={onViewAllGroups}
            startIcon={<LaunchIcon />}
            sx={{
              backgroundColor: '#3b8c6e',
              '&:hover': {
                backgroundColor: '#2a6b4f',
              }
            }}
          >
            View All Groups
          </Button>
        </Box>

        <GroupsTable
          groups={processedGroups}
          handleGroupSelect={handleGroupSelect}
          handleMovesOnChange={handleMovesOnChange}
          handleNationalChange={handleNationalChange}
          handleCitationChange={handleCitationChange}
          handleCommendedChange={handleCommendedChange}
          showMovesOn={selectedClassification.phase === 1}
          showFinalAwards={showFinalAwards}
          showActionButton={false}
          limit={5}
          isCitationCategory={isCitationCategory}
          isPhase2Judge={isPhase2Judge}
        />

        {processedGroups.length > 5 && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
              Showing 5 of {processedGroups.length} groups
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              size="small"
              onClick={onViewAllGroups}
              endIcon={<LaunchIcon />}
            >
              View All Groups
            </Button>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default ClassificationSummaryView;
