import React, { useContext } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid
} from '@mui/material';
import { AppProviderStore } from '../../../AppStore';
import OverallProgressCard from './OverallProgressCard';
import CategoryCard from './CategoryCard';
import JudgeIntroVideo from '../../JudgeIntroVideo/JudgeIntroVideo';

const SummaryView = ({
  judge,
  summaryData,
  overallProgress,
  onClassificationClick
}) => {
  const { AppStore } = useContext(AppProviderStore);

  const handleEditIntro = () => {
    // Navigate back to the intro recording interface
    AppStore.setJudgeVideoCreated(false);
    AppStore.setIsBanner(false);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, color: '#3b8c6e' }}>
        Judge Dashboard
      </Typography>

      <Typography variant="h6" sx={{ mb: 2 }}>
        Welcome, {judge?.name || 'Judge'}
      </Typography>

      {/* Judge Introduction Video Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          {/* Overall Progress Summary */}
          <OverallProgressCard overallProgress={overallProgress} />
        </Grid>
        <Grid item xs={12} md={6}>
          {/* Judge Intro Video */}
          <JudgeIntroVideo
            judge={judge}
            onEditClick={handleEditIntro}
            title="Your Introduction Video"
            compact={false}
          />
        </Grid>
      </Grid>

      <Typography variant="body1" sx={{ mb: 4 }}>
        Here's a summary of your judging assignments by category. You can select a classification below or use the sidebar to navigate to groups for scoring.
      </Typography>

      {summaryData.length > 0 ? (
        <Grid container spacing={3}>
          {summaryData.map((category) => (
            <Grid item xs={12} key={category.id}>
              <CategoryCard 
                category={category} 
                onClassificationClick={onClassificationClick} 
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            No assignments found. Please contact the administrator if you believe this is an error.
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default SummaryView;
