import React, { useState } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Badge,
  Button,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  HourglassEmpty as HourglassEmptyIcon,
  EmojiEvents as TrophyIcon,
  Star as StarIcon,
  Flag as FlagIcon,
  School as SchoolIcon,
  ThumbUp as ThumbUpIcon
} from '@mui/icons-material';

const CategorizedGroupsView = ({
  groups,
  handleGroupSelect,
  handleMovesOnChange,
  handleNationalChange,
  handleCitationChange,
  handleCommendedChange,
  showMovesOn = false,
  showFinalAwards = false,
  showActionButton = true,
  isPhase1 = false,
  isCitationCategory = false,
  isPhase2Judge = false
}) => {
  // State to track which sections are expanded
  const [expanded, setExpanded] = useState({
    movingOn: true,
    national: false,
    citation: false,
    state: false,
    commended: false,
    unscored: false,
    other: false
  });

  const handleAccordionChange = (section) => (event, isExpanded) => {
    setExpanded(prev => ({
      ...prev,
      [section]: isExpanded
    }));
  };

  // Categorize groups
  const categorizeGroups = (groups) => {
    const categories = {
      movingOn: [],
      national: [],
      citation: [],
      state: [],
      commended: [],
      unscored: [],
      other: []
    };

    groups.forEach(group => {
      if (!group.hasScore) {
        categories.unscored.push(group);
      } else if (isPhase1 && group.movesOn) {
        categories.movingOn.push(group);
      } else if (showFinalAwards) {
        // Phase 2 categorization - check for National/Citation and Commended awards
        const hasNational = group.isNational;
        const hasCitation = group.isCitation;
        const hasState = group.isState || (group.judgeScores && group.judgeScores.some(score => score.isState));
        const hasCommended = group.isCommended;

        if (isCitationCategory && hasCitation) {
          categories.citation.push(group);
        } else if (!isCitationCategory && hasNational) {
          categories.national.push(group);
        } else if (hasState) {
          categories.state.push(group);
        } else if (hasCommended) {
          categories.commended.push(group);
        } else {
          categories.other.push(group);
        }
      } else {
        categories.other.push(group);
      }
    });

    return categories;
  };

  const categorizedGroups = categorizeGroups(groups);

  // Configuration for each category
  const categoryConfig = {
    movingOn: {
      title: 'Groups Moving On to Phase 2',
      icon: <FlagIcon />,
      color: '#3f51b5',
      show: isPhase1
    },
    national: {
      title: 'National Winners',
      icon: <TrophyIcon />,
      color: '#d4af37',
      show: showFinalAwards && !isCitationCategory
    },
    citation: {
      title: 'Citation Awards',
      icon: <StarIcon />,
      color: '#d4af37', // Use same color as National for consistency
      show: showFinalAwards && isCitationCategory
    },
    state: {
      title: 'State Winners',
      icon: <SchoolIcon />,
      color: '#4caf50',
      show: showFinalAwards
    },
    commended: {
      title: 'Commended Performances',
      icon: <ThumbUpIcon />,
      color: '#c0392b',
      show: showFinalAwards
    },
    unscored: {
      title: 'Unscored Groups',
      icon: <HourglassEmptyIcon />,
      color: '#ff9800',
      show: true
    },
    other: {
      title: 'Other Groups',
      icon: <CheckCircleIcon />,
      color: '#607d8b',
      show: true
    }
  };

  const renderGroupTable = (groups, categoryKey) => {
    if (groups.length === 0) return null;

    return (
      <TableContainer component={Paper} sx={{ boxShadow: 1 }}>
        <Table size="small">
          <TableHead>
            <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.04)' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>Group</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Score</TableCell>
              {showMovesOn && categoryKey === 'other' && (
                <TableCell sx={{ fontWeight: 'bold' }}>Moves On</TableCell>
              )}
              {showFinalAwards && categoryKey === 'other' && (
                <>
                  <TableCell sx={{ fontWeight: 'bold' }}>{isCitationCategory ? 'Citation' : 'National'}</TableCell>
                  {!isPhase2Judge && (
                    <TableCell sx={{ fontWeight: 'bold' }}>Commended</TableCell>
                  )}
                </>
              )}
              {showActionButton && (
                <TableCell sx={{ fontWeight: 'bold' }}>Action</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {groups.map((group) => (
              <TableRow
                key={group._id}
                sx={{
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    cursor: showActionButton ? 'default' : 'pointer'
                  }
                }}
                onClick={showActionButton ? undefined : () => handleGroupSelect(group)}
              >
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      Group: {group.maskedName}
                    </Typography>
                    {showMovesOn && group.isTopScore && (
                      <Tooltip title="This group is in the top 50% based on score">
                        <Chip
                          size="small"
                          label="Top 50%"
                          sx={{
                            ml: 1,
                            backgroundColor: '#3f51b5',
                            color: 'white',
                            height: '20px',
                            fontSize: '0.7rem'
                          }}
                        />
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  {group.hasScore ? (
                    <Chip
                      icon={<CheckCircleIcon sx={{ color: 'white' }} />}
                      label="Scored"
                      size="small"
                      sx={{
                        backgroundColor: '#3b8c6e',
                        color: 'white',
                        '& .MuiChip-icon': { color: 'white' }
                      }}
                    />
                  ) : (
                    <Chip
                      icon={<HourglassEmptyIcon sx={{ color: 'white' }} />}
                      label="Not Scored"
                      size="small"
                      sx={{
                        backgroundColor: '#ff9800',
                        color: 'white',
                        '& .MuiChip-icon': { color: 'white' }
                      }}
                    />
                  )}
                </TableCell>
                <TableCell>
                  {group.score !== null ? (
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {group.score}
                    </Typography>
                  ) : (
                    <Typography variant="body2" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                      No score
                    </Typography>
                  )}
                </TableCell>
                {showMovesOn && categoryKey === 'other' && (
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Checkbox
                        checked={Boolean(group.movesOn)}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleMovesOnChange(group, e.target.checked);
                        }}
                        disabled={!group.hasScore}
                        sx={{
                          color: '#3b8c6e',
                          '&.Mui-checked': {
                            color: '#3b8c6e',
                          },
                        }}
                        onClick={(e) => e.stopPropagation()}
                      />
                      <Typography
                        variant="body2"
                        sx={{
                          ml: 1,
                          color: group.hasScore ? 'text.primary' : 'text.disabled',
                          fontStyle: !group.hasScore ? 'italic' : 'normal'
                        }}
                      >
                        {group.movesOn ? 'Selected' : 'Not Selected'}
                      </Typography>
                    </Box>
                  </TableCell>
                )}
                {showFinalAwards && categoryKey === 'other' && (
                  <>
                    {/* National/Citation Checkbox */}
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Checkbox
                          checked={isCitationCategory ? Boolean(group.isCitation) : Boolean(group.isNational)}
                          onChange={(e) => {
                            e.stopPropagation();
                            if (isCitationCategory) {
                              handleCitationChange(group, e.target.checked);
                            } else {
                              handleNationalChange(group, e.target.checked);
                            }
                          }}
                          disabled={!group.hasScore}
                          sx={{
                            color: '#d4af37',
                            '&.Mui-checked': {
                              color: '#d4af37',
                            },
                          }}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <Typography
                          variant="body2"
                          sx={{
                            ml: 1,
                            color: group.hasScore ? 'text.primary' : 'text.disabled',
                            fontStyle: !group.hasScore ? 'italic' : 'normal'
                          }}
                        >
                          {(isCitationCategory ? group.isCitation : group.isNational)
                            ? (isCitationCategory ? 'Citation' : 'National')
                            : 'Not Selected'}
                        </Typography>
                      </Box>
                    </TableCell>
                    {/* Commended Checkbox (hidden for Phase 2 judges) */}
                    {!isPhase2Judge && (
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Checkbox
                            checked={Boolean(group.isCommended)}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleCommendedChange(group, e.target.checked);
                            }}
                            disabled={!group.hasScore}
                            sx={{
                              color: '#c0392b',
                              '&.Mui-checked': {
                                color: '#c0392b',
                              },
                            }}
                            onClick={(e) => e.stopPropagation()}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              ml: 1,
                              color: group.hasScore ? 'text.primary' : 'text.disabled',
                              fontStyle: !group.hasScore ? 'italic' : 'normal'
                            }}
                          >
                            {group.isCommended ? 'Commended' : 'Not Selected'}
                          </Typography>
                        </Box>
                      </TableCell>
                    )}
                  </>
                )}
                {showActionButton && (
                  <TableCell>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleGroupSelect(group);
                      }}
                      sx={{
                        backgroundColor: '#3b8c6e',
                        '&:hover': {
                          backgroundColor: '#2a6b4f',
                        }
                      }}
                    >
                      {group.hasScore ? 'Edit Score' : 'Score Now'}
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <Box sx={{ width: '100%' }}>
      {Object.entries(categoryConfig).map(([key, config]) => {
        if (!config.show || categorizedGroups[key].length === 0) return null;

        return (
          <Accordion
            key={key}
            expanded={expanded[key]}
            onChange={handleAccordionChange(key)}
            sx={{ mb: 2, boxShadow: 2 }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                backgroundColor: `${config.color}15`,
                '&:hover': {
                  backgroundColor: `${config.color}25`,
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <Box sx={{ color: config.color, mr: 1 }}>
                  {config.icon}
                </Box>
                <Typography variant="h6" sx={{ color: config.color, flexGrow: 1 }}>
                  {config.title}
                </Typography>
                <Badge
                  badgeContent={categorizedGroups[key].length}
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor: config.color,
                      color: 'white'
                    }
                  }}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 2 }}>
              {renderGroupTable(categorizedGroups[key], key)}
            </AccordionDetails>
          </Accordion>
        );
      })}
    </Box>
  );
};

export default CategorizedGroupsView;
