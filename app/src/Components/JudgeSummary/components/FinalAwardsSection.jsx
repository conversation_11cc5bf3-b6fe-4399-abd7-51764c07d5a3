import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  CircularProgress,
  Checkbox,
  Tooltip,
  Divider
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  Star as StarIcon,
  CheckCircle as CheckCircleIcon,
  ToggleOff as ToggleOffIcon
} from '@mui/icons-material';

const FinalAwardsSection = ({
  sortedGroups,
  isAutoSelectLoading,
  handleNationalChange,
  handleCitationChange,
  handleCommendedChange,
  handleAutoSelectNational,
  handleAutoSelectCitation,
  handleAutoSelectCommended,
  isCitationCategory = false,
  isPhase2Judge = false
}) => {
  const [isBatchUnselectLoading, setIsBatchUnselectLoading] = useState(false);

  // Function to handle batch unselect of all National/Citation groups
  const handleBatchUnselectNational = async () => {
    console.log(`Batch unselecting all ${isCitationCategory ? 'Citation' : 'National'} groups`);
    setIsBatchUnselectLoading(true);

    try {
      const targetGroups = isCitationCategory
        ? sortedGroups.filter(group => group.isCitation)
        : sortedGroups.filter(group => group.isNational);

      for (const group of targetGroups) {
        if (isCitationCategory) {
          await handleCitationChange(group, false);
        } else {
          await handleNationalChange(group, false);
        }
      }
    } catch (error) {
      console.error(`Error batch unselecting ${isCitationCategory ? 'Citation' : 'National'} groups:`, error);
    } finally {
      setIsBatchUnselectLoading(false);
    }
  };

  // Function to handle batch unselect of all Commended groups
  const handleBatchUnselectCommended = async () => {
    console.log('Batch unselecting all Commended groups');
    setIsBatchUnselectLoading(true);

    try {
      const commendedGroups = sortedGroups.filter(group => group.isCommended);
      
      for (const group of commendedGroups) {
        await handleCommendedChange(group, false);
      }
    } catch (error) {
      console.error('Error batch unselecting Commended groups:', error);
    } finally {
      setIsBatchUnselectLoading(false);
    }
  };

  const nationalGroups = isCitationCategory
    ? sortedGroups.filter(group => group.isCitationEligible)
    : sortedGroups.filter(group => group.isNationalEligible);
  const commendedGroups = sortedGroups.filter(group => group.isCommendedEligible);

  return (
    <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
      <Typography variant="h6" sx={{ mb: 1.5, color: '#3b8c6e', display: 'flex', alignItems: 'center' }}>
        <TrophyIcon sx={{ mr: 1 }} />
        Final Awards Selection
      </Typography>
      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        System suggestion: These lists are suggestions based on scores. You must check the boxes to confirm winners. The system does not automatically select winners.
      </Typography>

      {/* National/Citation Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, color: '#d4af37', display: 'flex', alignItems: 'center', fontWeight: 'bold' }}>
          <TrophyIcon sx={{ mr: 1, color: '#d4af37' }} />
          {isCitationCategory
            ? 'Citation Awards (Suggested)'
            : (isPhase2Judge ? 'National Awards (Top 50% Suggested)' : 'National Awards (Top 25% Suggested)')}
        </Typography>

        <Box sx={{ mb: 3 }}>
          {nationalGroups.length > 0 ? (
            <Grid container spacing={1}>
              {nationalGroups
                .sort((a, b) => b.score - a.score)
                .map(group => (
                  <Grid item xs={12} sm={6} md={4} key={group._id}>
                    <Box
                      sx={{
                        p: 1,
                        border: '1px solid #e0e0e0',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: group.isNational ? 'rgba(212, 175, 55, 0.1)' : 'transparent',
                        '&:hover': {
                          backgroundColor: 'rgba(212, 175, 55, 0.05)'
                        }
                      }}
                    >
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          Group: {group.maskedName}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Score: {group.score}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Tooltip title={
                          isCitationCategory
                            ? (group.isCitation ? "Remove Citation award" : "Award Citation")
                            : (group.isNational ? "Remove National award" : "Award National")
                        }>
                          <Checkbox
                            checked={isCitationCategory ? Boolean(group.isCitation) : Boolean(group.isNational)}
                            onChange={(e) => {
                              if (isCitationCategory) {
                                handleCitationChange(group, e.target.checked);
                              } else {
                                handleNationalChange(group, e.target.checked);
                              }
                            }}
                            disabled={!group.hasScore}
                            size="small"
                            sx={{
                              color: '#d4af37',
                              '&.Mui-checked': {
                                color: '#d4af37',
                              },
                            }}
                          />
                        </Tooltip>

                        {(isCitationCategory ? group.isCitation : group.isNational) ? (
                          <Chip
                            size="small"
                            label={isCitationCategory ? "Citation" : "National"}
                            sx={{ backgroundColor: '#d4af37', color: 'white' }}
                          />
                        ) : (
                          <Chip
                            size="small"
                            label="Not Selected"
                            variant="outlined"
                            sx={{ borderColor: '#d4af37', color: '#d4af37' }}
                          />
                        )}
                      </Box>
                    </Box>
                  </Grid>
                ))
              }
            </Grid>
          ) : (
            <Typography variant="body2" color="textSecondary" sx={{ fontStyle: 'italic' }}>
              No groups eligible for National awards based on current scores.
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <Button
            variant="outlined"
            color="error"
            size="small"
            onClick={handleBatchUnselectNational}
            disabled={
              (isCitationCategory
                ? sortedGroups.filter(group => group.isCitation).length === 0
                : sortedGroups.filter(group => group.isNational).length === 0) ||
              isBatchUnselectLoading ||
              isAutoSelectLoading
            }
            startIcon={isBatchUnselectLoading ? null : <ToggleOffIcon />}
          >
            {isBatchUnselectLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                Unselecting...
              </Box>
            ) : (
              `Unselect All ${isCitationCategory ? 'Citation' : 'National'}`
            )}
          </Button>

          <Button
            variant="outlined"
            size="small"
            onClick={isCitationCategory ? handleAutoSelectCitation : handleAutoSelectNational}
            disabled={
              (isCitationCategory
                ? nationalGroups.filter(group => !group.isCitation).length === 0
                : nationalGroups.filter(group => !group.isNational).length === 0) ||
              isAutoSelectLoading ||
              isBatchUnselectLoading
            }
            startIcon={isAutoSelectLoading ? null : <CheckCircleIcon />}
            sx={{
              borderColor: '#d4af37',
              color: '#d4af37',
              '&:hover': {
                borderColor: '#b8941f',
                backgroundColor: 'rgba(212, 175, 55, 0.04)'
              }
            }}
          >
            {isAutoSelectLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                Selecting...
              </Box>
            ) : (
              `Select All ${isCitationCategory ? 'Citation' : 'National'}`
            )}
          </Button>
        </Box>
      </Box>

      <Divider sx={{ my: 3 }} />

      {/* Commended Section: hide entirely for Phase 2 judges */}
      {!isPhase2Judge && (
        <Box>
          <Typography variant="subtitle1" sx={{ mb: 2, color: '#c0392b', display: 'flex', alignItems: 'center', fontWeight: 'bold' }}>
            <StarIcon sx={{ mr: 1, color: '#c0392b' }} />
            Commended Awards (Next 25% Suggested)
          </Typography>

        <Box sx={{ mb: 3 }}>
          {commendedGroups.length > 0 ? (
            <Grid container spacing={1}>
              {commendedGroups
                .sort((a, b) => b.score - a.score)
                .map(group => (
                  <Grid item xs={12} sm={6} md={4} key={group._id}>
                    <Box
                      sx={{
                        p: 1,
                        border: '1px solid #e0e0e0',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: group.isCommended ? 'rgba(192, 57, 43, 0.1)' : 'transparent',
                        '&:hover': {
                          backgroundColor: 'rgba(192, 57, 43, 0.05)'
                        }
                      }}
                    >
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          Group: {group.maskedName}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Score: {group.score}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Tooltip title={group.isCommended ? "Remove Commended award" : "Award Commended"}>
                          <Checkbox
                            checked={Boolean(group.isCommended)}
                            onChange={(e) => handleCommendedChange(group, e.target.checked)}
                            disabled={!group.hasScore}
                            size="small"
                            sx={{
                              color: '#c0392b',
                              '&.Mui-checked': {
                                color: '#c0392b',
                              },
                            }}
                          />
                        </Tooltip>

                        {group.isCommended ? (
                          <Chip
                            size="small"
                            label="Commended"
                            sx={{ backgroundColor: '#c0392b', color: 'white' }}
                          />
                        ) : (
                          <Chip
                            size="small"
                            label="Not Selected"
                            variant="outlined"
                            sx={{ borderColor: '#c0392b', color: '#c0392b' }}
                          />
                        )}
                      </Box>
                    </Box>
                  </Grid>
                ))
              }
            </Grid>
          ) : (
            <Typography variant="body2" color="textSecondary" sx={{ fontStyle: 'italic' }}>
              No groups eligible for Commended awards based on current scores.
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            color="error"
            size="small"
            onClick={handleBatchUnselectCommended}
            disabled={
              sortedGroups.filter(group => group.isCommended).length === 0 ||
              isBatchUnselectLoading ||
              isAutoSelectLoading
            }
            startIcon={isBatchUnselectLoading ? null : <ToggleOffIcon />}
          >
            {isBatchUnselectLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                Unselecting...
              </Box>
            ) : (
              'Unselect All Commended'
            )}
          </Button>

          <Button
            variant="outlined"
            size="small"
            onClick={handleAutoSelectCommended}
            disabled={
              commendedGroups.filter(group => !group.isCommended).length === 0 ||
              isAutoSelectLoading ||
              isBatchUnselectLoading
            }
            startIcon={isAutoSelectLoading ? null : <CheckCircleIcon />}
            sx={{
              borderColor: '#c0392b',
              color: '#c0392b',
              '&:hover': {
                borderColor: '#a93226',
                backgroundColor: 'rgba(192, 57, 43, 0.04)'
              }
            }}
          >
            {isAutoSelectLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                Selecting...
              </Box>
            ) : (
              'Select All Commended'
            )}
          </Button>
        </Box>
      </Box>
      )}
    </Paper>
  );
};

export default FinalAwardsSection;
