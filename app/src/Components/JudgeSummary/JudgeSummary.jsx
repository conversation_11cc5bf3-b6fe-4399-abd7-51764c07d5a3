import React, { useContext, useEffect, useState } from 'react';
import { AppProviderStore } from '../../AppStore';
import { observer } from 'mobx-react';

// Import the extracted components
import {
  SummaryView,
  ClassificationSummaryView,
  GroupsView
} from './components';

const JudgeSummary = ({ handleSelect }) => {
  const { AppStore } = useContext(AppProviderStore);
  const { assignments, judge } = AppStore;
  const [summaryData, setSummaryData] = useState([]);
  const [currentView, setCurrentView] = useState('summary'); // 'summary', 'classificationSummary', or 'groups'
  const [selectedClassification, setSelectedClassification] = useState(null);

  // State for sorting groups
  const [sortConfig, setSortConfig] = useState({
    key: 'maskedName',
    direction: 'asc'
  });

  // State for processed groups with score information
  const [processedGroups, setProcessedGroups] = useState([]);
  const [sortedGroups, setSortedGroups] = useState([]);

  // State for loading indicators
  const [isAutoSelectLoading, setIsAutoSelectLoading] = useState(false);

  // State to prevent unwanted navigation during updates
  const [isUpdatingMovesOn, setIsUpdatingMovesOn] = useState(false);

  // Function to handle sorting
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Function to get score for a group
  const getGroupScore = (group, classification = selectedClassification) => {
    let score = null;

    if (!group.classifications || !group.classifications.length) {
      // Check for score in group.judgeScores
      const judgeScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      if (judgeScore && judgeScore.score !== null && judgeScore.score !== undefined && judgeScore.score !== "") {
        score = parseFloat(judgeScore.score);
      }
    } else if (classification) {
      // Find the classification that matches
      // Handle both cases: classification.id might be a string or an object with _id
      const classificationId = typeof classification.id === 'string'
        ? classification.id
        : classification.id?._id;

      const matchingClassification = group.classifications.find(c => {
        if (typeof c.id === 'string') {
          return c.id === classificationId;
        } else if (typeof c.id === 'object' && c.id._id) {
          return c.id._id === classificationId;
        }
        return false;
      });

      if (matchingClassification?.judgeScores) {
        const judgeScore = matchingClassification.judgeScores.find(score => score.judgeId === judge?._id);
        if (judgeScore && judgeScore.score !== null && judgeScore.score !== undefined && judgeScore.score !== "") {
          score = parseFloat(judgeScore.score);
        }
      }
    }

    return score;
  };

  // Function to check if a group is marked as "Moves On"
  const getGroupMovesOn = (group, classification = selectedClassification) => {
    let movesOn = false;

    if (!group.classifications || !group.classifications.length) {
      // Check for movesOn in group.judgeScores
      const judgeScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      if (judgeScore) {
        movesOn = Boolean(judgeScore.movesOn);
      }
    } else if (classification) {
      // Find the classification that matches
      const classificationId = typeof classification.id === 'string'
        ? classification.id
        : classification.id?._id;

      const matchingClassification = group.classifications.find(c => {
        if (typeof c.id === 'string') {
          return c.id === classificationId;
        } else if (typeof c.id === 'object' && c.id._id) {
          return c.id._id === classificationId;
        }
        return false;
      });

      if (matchingClassification?.judgeScores) {
        const judgeScore = matchingClassification.judgeScores.find(score => score.judgeId === judge?._id);
        if (judgeScore) {
          movesOn = Boolean(judgeScore.movesOn);
        }
      }
    }

    return movesOn;
  };

  // Function to check if a group is marked as "National" or "Citation"
  const getGroupNational = (group, classification = selectedClassification) => {
    let isNational = false;

    if (!group.classifications || !group.classifications.length) {
      const judgeScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      if (judgeScore) {
        isNational = Boolean(judgeScore.isNational);
      }
    } else if (classification) {
      const classificationId = typeof classification.id === 'string'
        ? classification.id
        : classification.id?._id;

      const matchingClassification = group.classifications.find(c => {
        if (typeof c.id === 'string') {
          return c.id === classificationId;
        } else if (typeof c.id === 'object' && c.id._id) {
          return c.id._id === classificationId;
        }
        return false;
      });

      if (matchingClassification?.judgeScores) {
        const judgeScore = matchingClassification.judgeScores.find(score => score.judgeId === judge?._id);
        if (judgeScore) {
          isNational = Boolean(judgeScore.isNational);
        }
      }
    }

    return isNational;
  };

  // Function to check if a group is marked as "Citation"
  const getGroupCitation = (group, classification = selectedClassification) => {
    let isCitation = false;

    if (!group.classifications || !group.classifications.length) {
      const judgeScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      if (judgeScore) {
        isCitation = Boolean(judgeScore.isCitation);
      }
    } else if (classification) {
      const classificationId = typeof classification.id === 'string'
        ? classification.id
        : classification.id?._id;

      const matchingClassification = group.classifications.find(c => {
        if (typeof c.id === 'string') {
          return c.id === classificationId;
        } else if (typeof c.id === 'object' && c.id._id) {
          return c.id._id === classificationId;
        }
        return false;
      });

      if (matchingClassification?.judgeScores) {
        const judgeScore = matchingClassification.judgeScores.find(score => score.judgeId === judge?._id);
        if (judgeScore) {
          isCitation = Boolean(judgeScore.isCitation);
        }
      }
    }

    return isCitation;
  };

  // Function to check if a group is marked as "Commended"
  const getGroupCommended = (group, classification = selectedClassification) => {
    let isCommended = false;

    if (!group.classifications || !group.classifications.length) {
      const judgeScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      if (judgeScore) {
        isCommended = Boolean(judgeScore.isCommended);
      }
    } else if (classification) {
      const classificationId = typeof classification.id === 'string'
        ? classification.id
        : classification.id?._id;

      const matchingClassification = group.classifications.find(c => {
        if (typeof c.id === 'string') {
          return c.id === classificationId;
        } else if (typeof c.id === 'object' && c.id._id) {
          return c.id._id === classificationId;
        }
        return false;
      });

      if (matchingClassification?.judgeScores) {
        const judgeScore = matchingClassification.judgeScores.find(score => score.judgeId === judge?._id);
        if (judgeScore) {
          isCommended = Boolean(judgeScore.isCommended);
        }
      }
    }

    return isCommended;
  };

  // Function to check if a group has audio memos from the current judge
  const getGroupHasAudioMemos = (group) => {
    if (!group.tracks || !Array.isArray(group.tracks)) {
      return false;
    }

    return group.tracks.some(track => {
      if (!track.memos || !track.memos.audioMemos || !Array.isArray(track.memos.audioMemos)) {
        return false;
      }
      return track.memos.audioMemos.some(memo => memo.judge === judge?._id);
    });
  };

  // Function to check if a group has notes (performance or soloist) from the current judge
  const getGroupHasNotes = (group) => {
    if (!group.tracks || !Array.isArray(group.tracks)) {
      return false;
    }

    return group.tracks.some(track => {
      if (!track.memos) {
        return false;
      }

      // Check performance notes
      const hasPerformanceNotes = track.memos.notes &&
        Array.isArray(track.memos.notes) &&
        track.memos.notes.some(note =>
          note.judge === judge?._id &&
          note.content &&
          note.content.trim().length > 0
        );

      // Check soloist notes
      const hasSoloistNotes = track.memos.soloistNotes &&
        Array.isArray(track.memos.soloistNotes) &&
        track.memos.soloistNotes.some(note =>
          note.judge === judge?._id &&
          note.content &&
          note.content.trim().length > 0
        );

      return hasPerformanceNotes || hasSoloistNotes;
    });
  };

  // Function to calculate which groups are in the top 50% by score
  const calculateTopGroups = (groups) => {
    // Filter groups that have valid scores
    const scoredGroups = groups.filter(group =>
      group.score !== null && !isNaN(group.score) && group.score > 0
    );

    // Sort groups by score in descending order
    const sortedGroups = [...scoredGroups].sort((a, b) => b.score - a.score);

    // Calculate how many groups make up the top 50%
    const topCount = Math.ceil(sortedGroups.length / 2);

    // Get the minimum score that qualifies for top 50%
    const minTopScore = sortedGroups.length > 0 && topCount > 0
      ? sortedGroups[Math.min(topCount - 1, sortedGroups.length - 1)].score
      : 0;

    // Create a Set of group IDs in the top 50%
    const topGroupIds = new Set();
    sortedGroups.forEach((group, index) => {
      if (index < topCount || group.score >= minTopScore) {
        topGroupIds.add(group._id);
      }
    });

    return {
      topGroupIds,
      minTopScore,
      topCount
    };
  };

  // Function to calculate suggested National and Commended groups
  // options.useTop50ForNational: when true, suggest top 50% as National and the rest (scored) as Commended
  // when false (default), suggest top 25% as National and next 25% as Commended
  const calculateFinalAwards = (groups, options = { useTop50ForNational: false }) => {
    const { useTop50ForNational } = options;

    // Filter groups that have valid scores
    const scoredGroups = groups.filter(group =>
      group.score !== null && !isNaN(group.score) && group.score > 0
    );

    // Sort groups by score in descending order
    const sortedGroups = [...scoredGroups].sort((a, b) => b.score - a.score);

    // Determine counts based on mode
    const nationalCount = useTop50ForNational
      ? Math.ceil(sortedGroups.length / 2)
      : Math.ceil(sortedGroups.length / 4);

    const commendedCount = useTop50ForNational
      ? Math.max(sortedGroups.length - nationalCount, 0)
      : Math.ceil(sortedGroups.length / 4);

    // Create Sets for National and Commended group IDs
    const nationalGroupIds = new Set();
    const commendedGroupIds = new Set();

    sortedGroups.forEach((group, index) => {
      if (index < nationalCount) {
        nationalGroupIds.add(group._id);
      } else if (!useTop50ForNational && index < nationalCount + commendedCount) {
        // Default mode: next 25% are commended
        commendedGroupIds.add(group._id);
      } else if (useTop50ForNational) {
        // Phase 2 mode: remaining scored groups are commended suggestions
        commendedGroupIds.add(group._id);
      }
    });

    return {
      nationalGroupIds,
      commendedGroupIds,
      nationalCount,
      commendedCount,
      sortedGroups
    };
  };

  // Function to process groups with score information
  const processGroups = (groups, classification = selectedClassification) => {
    // First, process basic group information
    const processedGroups = groups.map(group => {
      // Get the masked name
      const maskedName = group.maskedName ||
        (group.classifications && classification && group.classifications.find(c => {
          const classificationId = typeof classification.id === 'string' ? classification.id : classification.id?._id;
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        })?.maskedName) || '';

      // Get the score
      const score = getGroupScore(group, classification);

      // Get the movesOn status
      const movesOn = getGroupMovesOn(group, classification);

      // Get the National, Citation, and Commended status
      const isNational = getGroupNational(group, classification);
      const isCitation = getGroupCitation(group, classification);
      const isCommended = getGroupCommended(group, classification);

      // Check if the group has memos and notes from the current judge
      const hasAudioMemos = getGroupHasAudioMemos(group);
      const hasNotes = getGroupHasNotes(group);

      // Determine if the group has been scored
      const hasScore = score !== null && !isNaN(score) && score > 0;

      return {
        ...group,
        maskedName,
        score,
        hasScore,
        movesOn,
        isNational,
        isCitation,
        isCommended,
        hasAudioMemos,
        hasNotes,
        isTopScore: false, // Will be set in the next step
        isNationalEligible: false, // Will be set for Phase 2 judges
        isCitationEligible: false, // Will be set for Citation categories
        isCommendedEligible: false // Will be set for Phase 2 judges
      };
    });

    // Determine phase handling:
    // - Phase 2: use top 50% as National suggestions; remaining scored as Commended suggestions
    // - No phase/Not specified: keep original 25% National + next 25% Commended suggestions
    // - Phase 1: compute top 50% for moves on
    const phaseVal = classification?.phase;
    const isPhase2 = phaseVal === 2 || String(phaseVal) === '2';
    const isNoPhase = !phaseVal || phaseVal === 'Not specified' || String(phaseVal).trim() === '';

    if (isPhase2 || isNoPhase) {
      // Check if this is a Citation category
      const categoryName = AppStore.currentSelectedCategory?.name || '';
      const isCitationCategory = categoryName.toLowerCase().includes('citation');

      // Calculate with mode depending on phase
      const { nationalGroupIds, commendedGroupIds } = calculateFinalAwards(
        processedGroups,
        { useTop50ForNational: isPhase2 }
      );

      // Mark groups that are eligible for National/Citation and Commended
      return processedGroups.map(group => ({
        ...group,
        isNationalEligible: isCitationCategory ? false : nationalGroupIds.has(group._id),
        isCitationEligible: isCitationCategory ? nationalGroupIds.has(group._id) : false,
        isCommendedEligible: commendedGroupIds.has(group._id)
      }));
    } else {
      // Phase 1: Calculate which groups are in the top 50%
      const { topGroupIds } = calculateTopGroups(processedGroups);

      // Mark groups that are in the top 50%
      return processedGroups.map(group => ({
        ...group,
        isTopScore: topGroupIds.has(group._id)
      }));
    }
  };

  // Function to handle clicking on a classification
  const handleClassificationClick = (classification, categoryId) => {
    console.log('Classification clicked:', classification);

    // Find the original classification data from assignments
    const category = assignments.find(a => a.category?._id === categoryId);
    if (!category) {
      console.error('Category not found:', categoryId);
      return;
    }

    // The classification.id is already the string ID from the processed data
    const originalClassification = category.classifications.find(c => c.id?._id === classification.id);
    if (!originalClassification) {
      console.error('Classification not found in assignments:', classification.id);
      return;
    }

    // Check if the classification has groups
    if (!originalClassification.groups || originalClassification.groups.length === 0) {
      console.log('No groups available in this classification');
      return;
    }

    // Set the selected classification
    setSelectedClassification(originalClassification);

    // Update the AppStore with the selected category and classification
    AppStore.setCurrentSelectedCategory(category);
    AppStore.setCurrentSelectedClassification(originalClassification);

    // Process the groups with score information
    const processed = processGroups(originalClassification.groups, originalClassification);
    setProcessedGroups(processed);

    // Change the view to show classification summary
    setCurrentView('classificationSummary');
  };

  // Function to handle selecting a group
  const handleGroupSelect = (group) => {
    console.log('JudgeSummary: Group selected:', group._id);

    // Set up the classification context in AppStore if we have a selected classification
    if (selectedClassification) {
      // Find the classification index in the group's classifications array
      let classificationIndex = -1;

      if (group.classifications && group.classifications.length > 0) {
        // Handle both cases: when classification.id is a string or an object
        const classificationId = typeof selectedClassification.id === 'string'
          ? selectedClassification.id
          : selectedClassification.id?._id;

        classificationIndex = group.classifications.findIndex(c => {
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        });

        console.log(`JudgeSummary: Found classification index ${classificationIndex} for group ${group._id}`);
      }

      // Set the classification index in AppStore
      AppStore.selectedClassificationIndex = classificationIndex;

      // Load the existing judge score from the correct location
      let existingScore = null;
      if (classificationIndex >= 0 && group.classifications[classificationIndex]?.judgeScores) {
        existingScore = group.classifications[classificationIndex].judgeScores.find(
          score => score.judgeId === judge?._id
        );
      } else if (!group.classifications?.length && group.judgeScores) {
        existingScore = group.judgeScores.find(score => score.judgeId === judge?._id);
      }

      // Set the judge score in AppStore
      const scoreToSet = existingScore || {
        judgeId: judge?._id,
        score: null,
        movesOn: false,
        isCommended: false,
        isNational: false,
        isCitation: false,
        isState: false
      };

      AppStore.setJudgeScore(scoreToSet);
      console.log('JudgeSummary: Set judge score:', scoreToSet);
    }

    if (handleSelect && typeof handleSelect === 'function') {
      console.log('JudgeSummary: Calling parent handleSelect function');
      handleSelect(group);
    } else {
      console.log('JudgeSummary: No parent handleSelect function provided');
    }
  };

  // Function to handle "Moves On" checkbox changes
  const handleMovesOnChange = async (group, checked) => {
    console.log('JudgeSummary: Moves On changed for group:', group._id, 'to:', checked);

    // Set updating state to prevent unwanted navigation
    setIsUpdatingMovesOn(true);

    // Store current view state to prevent unwanted navigation
    const currentViewState = currentView;
    const currentSelectedClassificationState = selectedClassification;

    try {
      // Find the classification index for this group
      let classificationIndex = -1;
      let existingScore = null;

      if (!group.classifications || !group.classifications.length) {
        // No classifications, use regular group judge scores
        existingScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      } else if (selectedClassification) {
        // Find the classification index
        const classificationId = typeof selectedClassification.id === 'string'
          ? selectedClassification.id
          : selectedClassification.id?._id;

        classificationIndex = group.classifications.findIndex(c => {
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        });

        console.log(`JudgeSummary: Found classification index ${classificationIndex} for moves on change`);

        if (classificationIndex >= 0 && group.classifications[classificationIndex]?.judgeScores) {
          existingScore = group.classifications[classificationIndex].judgeScores.find(
            score => score.judgeId === judge?._id
          );
        }
      }

      // Create a score object with the updated movesOn value
      const scoreObj = {
        score: existingScore?.score || 0, // Use existing score or default to 0
        movesOn: checked,
        isCommended: existingScore?.isCommended || false,
        isNational: existingScore?.isNational || false,
        isCitation: existingScore?.isCitation || false,
        isState: existingScore?.isState || false
      };

      // Set the classification index in AppStore
      AppStore.selectedClassificationIndex = classificationIndex;

      // Update the score in AppStore
      AppStore.setJudgeScore(scoreObj);

      // Save the group with the current group selected
      const originalSelectedGroup = AppStore.selectedGroup;
      const originalClassificationIndex = AppStore.selectedClassificationIndex;

      AppStore.setGroup(group);
      AppStore.selectedClassificationIndex = classificationIndex;

      // Update the score in the database
      const updatedGroup = await AppStore.setGroupScore();

      // Restore the original selected group and classification index if needed
      if (originalSelectedGroup && originalSelectedGroup._id !== group._id) {
        AppStore.setGroup(originalSelectedGroup);
        AppStore.selectedClassificationIndex = originalClassificationIndex;
      }

      // Update the processed groups with the new data
      if (updatedGroup) {
        const updatedProcessedGroups = processedGroups.map(g =>
          g._id === updatedGroup._id ? { ...g, movesOn: checked } : g
        );
        setProcessedGroups(updatedProcessedGroups);
      }

      // Restore view state to prevent unwanted navigation
      if (currentViewState !== currentView) {
        setCurrentView(currentViewState);
      }
      if (currentSelectedClassificationState && currentSelectedClassificationState !== selectedClassification) {
        setSelectedClassification(currentSelectedClassificationState);
      }

      return true;
    } catch (error) {
      console.error('Error updating Moves On status:', error);
      return false;
    } finally {
      // Reset updating state
      setIsUpdatingMovesOn(false);
    }
  };

  // Function to handle "National" checkbox changes
  const handleNationalChange = async (group, checked) => {
    console.log('JudgeSummary: National changed for group:', group._id, 'to:', checked);

    setIsUpdatingMovesOn(true);
    const currentViewState = currentView;
    const currentSelectedClassificationState = selectedClassification;

    try {
      let classificationIndex = -1;
      let existingScore = null;

      if (!group.classifications || !group.classifications.length) {
        existingScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      } else if (selectedClassification) {
        const classificationId = typeof selectedClassification.id === 'string'
          ? selectedClassification.id
          : selectedClassification.id?._id;

        classificationIndex = group.classifications.findIndex(c => {
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        });

        if (classificationIndex >= 0 && group.classifications[classificationIndex]?.judgeScores) {
          existingScore = group.classifications[classificationIndex].judgeScores.find(
            score => score.judgeId === judge?._id
          );
        }
      }

      const scoreObj = {
        score: existingScore?.score || 0,
        movesOn: existingScore?.movesOn || false,
        isCommended: checked ? false : (existingScore?.isCommended || false), // Clear commended if national is selected
        isNational: checked,
        isCitation: checked ? false : (existingScore?.isCitation || false), // Clear citation if national is selected
        isState: existingScore?.isState || false
      };

      AppStore.selectedClassificationIndex = classificationIndex;
      AppStore.setJudgeScore(scoreObj);

      const originalSelectedGroup = AppStore.selectedGroup;
      const originalClassificationIndex = AppStore.selectedClassificationIndex;

      AppStore.setGroup(group);
      AppStore.selectedClassificationIndex = classificationIndex;

      const updatedGroup = await AppStore.setGroupScore();

      if (originalSelectedGroup && originalSelectedGroup._id !== group._id) {
        AppStore.setGroup(originalSelectedGroup);
        AppStore.selectedClassificationIndex = originalClassificationIndex;
      }

      if (updatedGroup) {
        const updatedProcessedGroups = processedGroups.map(g =>
          g._id === updatedGroup._id ? { ...g, isNational: checked, isCommended: checked ? false : g.isCommended, isCitation: checked ? false : g.isCitation } : g
        );
        setProcessedGroups(updatedProcessedGroups);
      }

      if (currentViewState !== currentView) {
        setCurrentView(currentViewState);
      }
      if (currentSelectedClassificationState && currentSelectedClassificationState !== selectedClassification) {
        setSelectedClassification(currentSelectedClassificationState);
      }

      return true;
    } catch (error) {
      console.error('Error updating National status:', error);
      return false;
    } finally {
      setIsUpdatingMovesOn(false);
    }
  };

  // Function to handle "Citation" checkbox changes
  const handleCitationChange = async (group, checked) => {
    console.log('JudgeSummary: Citation changed for group:', group._id, 'to:', checked);

    setIsUpdatingMovesOn(true);
    const currentViewState = currentView;
    const currentSelectedClassificationState = selectedClassification;

    try {
      let classificationIndex = -1;
      let existingScore = null;

      if (!group.classifications || !group.classifications.length) {
        existingScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      } else if (selectedClassification) {
        const classificationId = typeof selectedClassification.id === 'string'
          ? selectedClassification.id
          : selectedClassification.id?._id;

        classificationIndex = group.classifications.findIndex(c => {
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        });

        if (classificationIndex >= 0 && group.classifications[classificationIndex]?.judgeScores) {
          existingScore = group.classifications[classificationIndex].judgeScores.find(
            score => score.judgeId === judge?._id
          );
        }
      }

      const scoreObj = {
        score: existingScore?.score || 0,
        movesOn: existingScore?.movesOn || false,
        isCommended: checked ? false : (existingScore?.isCommended || false), // Clear commended if citation is selected
        isNational: checked ? false : (existingScore?.isNational || false), // Clear national if citation is selected
        isCitation: checked,
        isState: existingScore?.isState || false
      };

      AppStore.selectedClassificationIndex = classificationIndex;
      AppStore.setJudgeScore(scoreObj);

      const originalSelectedGroup = AppStore.selectedGroup;
      const originalClassificationIndex = AppStore.selectedClassificationIndex;

      AppStore.setGroup(group);
      AppStore.selectedClassificationIndex = classificationIndex;

      const updatedGroup = await AppStore.setGroupScore();

      if (originalSelectedGroup && originalSelectedGroup._id !== group._id) {
        AppStore.setGroup(originalSelectedGroup);
        AppStore.selectedClassificationIndex = originalClassificationIndex;
      }

      if (updatedGroup) {
        const updatedProcessedGroups = processedGroups.map(g =>
          g._id === updatedGroup._id ? { ...g, isCitation: checked, isCommended: checked ? false : g.isCommended, isNational: checked ? false : g.isNational } : g
        );
        setProcessedGroups(updatedProcessedGroups);
      }

      if (currentViewState !== currentView) {
        setCurrentView(currentViewState);
      }
      if (currentSelectedClassificationState && currentSelectedClassificationState !== selectedClassification) {
        setSelectedClassification(currentSelectedClassificationState);
      }

      return true;
    } catch (error) {
      console.error('Error updating Citation status:', error);
      return false;
    } finally {
      setIsUpdatingMovesOn(false);
    }
  };

  // Function to handle "Commended" checkbox changes
  const handleCommendedChange = async (group, checked) => {
    console.log('JudgeSummary: Commended changed for group:', group._id, 'to:', checked);

    setIsUpdatingMovesOn(true);
    const currentViewState = currentView;
    const currentSelectedClassificationState = selectedClassification;

    try {
      let classificationIndex = -1;
      let existingScore = null;

      if (!group.classifications || !group.classifications.length) {
        existingScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      } else if (selectedClassification) {
        const classificationId = typeof selectedClassification.id === 'string'
          ? selectedClassification.id
          : selectedClassification.id?._id;

        classificationIndex = group.classifications.findIndex(c => {
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        });

        if (classificationIndex >= 0 && group.classifications[classificationIndex]?.judgeScores) {
          existingScore = group.classifications[classificationIndex].judgeScores.find(
            score => score.judgeId === judge?._id
          );
        }
      }

      const scoreObj = {
        score: existingScore?.score || 0,
        movesOn: existingScore?.movesOn || false,
        isCommended: checked,
        isNational: checked ? false : (existingScore?.isNational || false), // Clear national if commended is selected
        isCitation: checked ? false : (existingScore?.isCitation || false), // Clear citation if commended is selected
        isState: existingScore?.isState || false
      };

      AppStore.selectedClassificationIndex = classificationIndex;
      AppStore.setJudgeScore(scoreObj);

      const originalSelectedGroup = AppStore.selectedGroup;
      const originalClassificationIndex = AppStore.selectedClassificationIndex;

      AppStore.setGroup(group);
      AppStore.selectedClassificationIndex = classificationIndex;

      const updatedGroup = await AppStore.setGroupScore();

      if (originalSelectedGroup && originalSelectedGroup._id !== group._id) {
        AppStore.setGroup(originalSelectedGroup);
        AppStore.selectedClassificationIndex = originalClassificationIndex;
      }

      if (updatedGroup) {
        const updatedProcessedGroups = processedGroups.map(g =>
          g._id === updatedGroup._id ? { ...g, isCommended: checked, isNational: checked ? false : g.isNational, isCitation: checked ? false : g.isCitation } : g
        );
        setProcessedGroups(updatedProcessedGroups);
      }

      if (currentViewState !== currentView) {
        setCurrentView(currentViewState);
      }
      if (currentSelectedClassificationState && currentSelectedClassificationState !== selectedClassification) {
        setSelectedClassification(currentSelectedClassificationState);
      }

      return true;
    } catch (error) {
      console.error('Error updating Commended status:', error);
      return false;
    } finally {
      setIsUpdatingMovesOn(false);
    }
  };

  // Function to automatically select all eligible groups for National awards
  const handleAutoSelectNational = async () => {
    console.log('Auto-selecting all eligible groups for National awards');

    try {
      setIsAutoSelectLoading(true);

      const groupsToCheck = sortedGroups.length > 0 ? sortedGroups : processedGroups;
      const nationalGroupsToUpdate = groupsToCheck.filter(group =>
        group.isNationalEligible && !group.isNational && group.hasScore
      );

      if (nationalGroupsToUpdate.length === 0) {
        console.log('No National groups to update');
        setIsAutoSelectLoading(false);
        return;
      }

      console.log(`Batch updating ${nationalGroupsToUpdate.length} groups for National awards`);

      for (const group of nationalGroupsToUpdate) {
        await handleNationalChange(group, true);
      }

      console.log('Successfully batch updated all National groups');

    } catch (error) {
      console.error('Error auto-selecting National groups:', error);
    } finally {
      setIsAutoSelectLoading(false);
    }
  };

  // Function to automatically select all eligible groups for Citation awards
  const handleAutoSelectCitation = async () => {
    console.log('Auto-selecting all eligible groups for Citation awards');

    try {
      setIsAutoSelectLoading(true);

      const groupsToCheck = sortedGroups.length > 0 ? sortedGroups : processedGroups;
      const citationGroupsToUpdate = groupsToCheck.filter(group =>
        group.isCitationEligible && !group.isCitation && group.hasScore
      );

      if (citationGroupsToUpdate.length === 0) {
        console.log('No Citation groups to update');
        setIsAutoSelectLoading(false);
        return;
      }

      console.log(`Batch updating ${citationGroupsToUpdate.length} groups for Citation awards`);

      for (const group of citationGroupsToUpdate) {
        await handleCitationChange(group, true);
      }

      console.log('Successfully batch updated all Citation groups');

    } catch (error) {
      console.error('Error auto-selecting Citation groups:', error);
    } finally {
      setIsAutoSelectLoading(false);
    }
  };

  // Function to automatically select all eligible groups for Commended awards
  const handleAutoSelectCommended = async () => {
    console.log('Auto-selecting all eligible groups for Commended awards');

    try {
      setIsAutoSelectLoading(true);

      const groupsToCheck = sortedGroups.length > 0 ? sortedGroups : processedGroups;
      const commendedGroupsToUpdate = groupsToCheck.filter(group =>
        group.isCommendedEligible && !group.isCommended && group.hasScore
      );

      if (commendedGroupsToUpdate.length === 0) {
        console.log('No Commended groups to update');
        setIsAutoSelectLoading(false);
        return;
      }

      console.log(`Batch updating ${commendedGroupsToUpdate.length} groups for Commended awards`);

      for (const group of commendedGroupsToUpdate) {
        await handleCommendedChange(group, true);
      }

      console.log('Successfully batch updated all Commended groups');

    } catch (error) {
      console.error('Error auto-selecting Commended groups:', error);
    } finally {
      setIsAutoSelectLoading(false);
    }
  };

  // Function to automatically select all top groups to move on
  const handleAutoSelectTopGroups = async () => {
    console.log('Auto-selecting all top groups to move on');

    try {
      // Set loading state
      setIsAutoSelectLoading(true);

      // Get all top groups that aren't already selected to move on
      const groupsToCheck = sortedGroups.length > 0 ? sortedGroups : processedGroups;
      const topGroupsToUpdate = groupsToCheck.filter(group =>
        group.isTopScore && !group.movesOn && group.hasScore
      );

      if (topGroupsToUpdate.length === 0) {
        console.log('No top groups to update');
        setIsAutoSelectLoading(false);
        return;
      }

      console.log(`Batch updating ${topGroupsToUpdate.length} groups to move on`);

      // Prepare batch update data
      const updates = topGroupsToUpdate.map(group => {
        // Find the classification index for this group
        let classificationIndex = -1;
        let existingScore = null;

        if (group.classifications && group.classifications.length && selectedClassification) {
          const classificationId = typeof selectedClassification.id === 'string'
            ? selectedClassification.id
            : selectedClassification.id?._id;

          classificationIndex = group.classifications.findIndex(c => {
            if (typeof c.id === 'string') {
              return c.id === classificationId;
            } else if (typeof c.id === 'object' && c.id._id) {
              return c.id._id === classificationId;
            }
            return false;
          });

          if (classificationIndex >= 0 && group.classifications[classificationIndex]?.judgeScores) {
            existingScore = group.classifications[classificationIndex].judgeScores.find(
              score => score.judgeId === judge?._id
            );
          }
        } else {
          existingScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
        }

        return {
          groupId: group._id,
          score: existingScore?.score || 0,
          movesOn: true, // Set to true for auto-select
          isCommended: existingScore?.isCommended || false,
          isNational: existingScore?.isNational || false,
          isCitation: existingScore?.isCitation || false,
          isState: existingScore?.isState || false,
          classificationIndex: classificationIndex >= 0 ? classificationIndex : undefined
        };
      });

      // Make batch API call
      const response = await fetch('/api/groups/batchUpdateGroupScores', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          updates,
          judgeId: judge._id
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to batch update group scores');
      }

      const result = await response.json();
      console.log(`Batch update completed: ${result.totalUpdated} successful, ${result.totalErrors} errors`);

      if (result.errors && result.errors.length > 0) {
        console.warn('Some updates failed:', result.errors);
      }

      // Update the processed groups with the new data
      const updatedProcessedGroups = processedGroups.map(group => {
        const wasUpdated = topGroupsToUpdate.some(updatedGroup => updatedGroup._id === group._id);
        return wasUpdated ? { ...group, movesOn: true } : group;
      });
      setProcessedGroups(updatedProcessedGroups);

      // Show success message
      console.log('Successfully batch updated all top groups');

    } catch (error) {
      console.error('Error auto-selecting top groups:', error);
    } finally {
      // Reset loading state
      setIsAutoSelectLoading(false);
    }
  };

  // Effect to handle sorting of processed groups
  useEffect(() => {
    if (processedGroups.length === 0) {
      setSortedGroups([]);
      return;
    }

    const sorted = [...processedGroups].sort((a, b) => {
      let aValue, bValue;

      switch (sortConfig.key) {
        case 'maskedName':
          aValue = a.maskedName || '';
          bValue = b.maskedName || '';
          break;
        case 'score':
          aValue = a.score !== null && !isNaN(a.score) ? parseFloat(a.score) : -1;
          bValue = b.score !== null && !isNaN(b.score) ? parseFloat(b.score) : -1;
          break;
        case 'hasScore':
          aValue = a.hasScore ? 1 : 0;
          bValue = b.hasScore ? 1 : 0;
          break;
        case 'movesOn':
          aValue = a.movesOn ? 1 : 0;
          bValue = b.movesOn ? 1 : 0;
          break;
        default:
          aValue = a.maskedName || '';
          bValue = b.maskedName || '';
      }

      // Handle string comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortConfig.direction === 'asc' ? comparison : -comparison;
      }

      // Handle numeric comparison
      if (sortConfig.direction === 'asc') {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    });

    setSortedGroups(sorted);
  }, [processedGroups, sortConfig]);

  // Effect to prevent unwanted navigation during moves on updates
  useEffect(() => {
    if (isUpdatingMovesOn && currentView === 'summary') {
      // If we're updating moves on and the view changed to summary, restore the classification view
      if (selectedClassification) {
        setCurrentView('classificationSummary');
      }
    }
  }, [currentView, isUpdatingMovesOn, selectedClassification]);

  useEffect(() => {
    console.log('Assignments data:', assignments);
    if (assignments && Array.isArray(assignments) && assignments.length > 0) {
      // Process the assignments data to create summary information
      const processedData = assignments.map(assignment => {
        const categoryName = assignment.category?.name;
        const categoryId = assignment.category?._id;

        // Process classifications under this category
        const classificationsData = assignment.classifications?.map(classification => {
          // Count scored vs unscored groups
          const totalGroups = classification.groups ? classification.groups.length : 0;
          const scoredGroups = classification.groups ? classification.groups.filter(group => {
            // Check if this group has been scored by the current judge
            let isScored = false;

            if (!group.classifications || !group.classifications.length) {
              isScored = group.judgeScores &&
                Array.isArray(group.judgeScores) &&
                group.judgeScores.some(score => {
                  // A score is only valid if it has a numeric value greater than 0
                  // We need to handle JavaScript type coercion where empty strings can become 0
                  // First check if the score belongs to the current judge
                  const isCurrentJudge = score.judgeId === judge?._id;

                  // Then check if the score has a valid numeric value
                  const hasNumericValue = score.score !== null &&
                    score.score !== undefined &&
                    score.score !== "" &&
                    !isNaN(score.score);

                  // Finally check if the score is greater than 0
                  // Note: We're explicitly checking for score > 0 to avoid counting 0 scores as valid
                  const isPositive = hasNumericValue && parseFloat(score.score) > 0;

                  // A score is only valid if it meets all criteria
                  const hasValidScore = isCurrentJudge && hasNumericValue && isPositive;

                  if (hasValidScore) {
                    console.log(`Group ${group._id} has valid score: ${score.score} (${typeof score.score})`);
                  } else if (isCurrentJudge) {
                    let reason = "";
                    if (!hasNumericValue) {
                      reason = "Not a valid number";
                    } else if (!isPositive) {
                      reason = "Score is not greater than 0";
                    }
                    console.log(`Group ${group._id} has INVALID score: ${score.score} (${typeof score.score}), reason: ${reason}, value: ${JSON.stringify(score)}`);
                  }

                  return hasValidScore;
                });
            } else {
              // Find the classification that matches - handle both string and object IDs
              const classificationId = classification.id?._id;
              const matchingClassification = group.classifications.find(c => {
                if (typeof c.id === 'string') {
                  return c.id === classificationId;
                } else if (typeof c.id === 'object' && c.id._id) {
                  return c.id._id === classificationId;
                }
                return false;
              });

              isScored = matchingClassification &&
                matchingClassification.judgeScores &&
                Array.isArray(matchingClassification.judgeScores) &&
                matchingClassification.judgeScores.some(score => {
                  // A score is only valid if it has a numeric value greater than 0
                  // We need to handle JavaScript type coercion where empty strings can become 0
                  // First check if the score belongs to the current judge
                  const isCurrentJudge = score.judgeId === judge?._id;

                  // Then check if the score has a valid numeric value
                  const hasNumericValue = score.score !== null &&
                    score.score !== undefined &&
                    score.score !== "" &&
                    !isNaN(score.score);

                  // Finally check if the score is greater than 0
                  // Note: We're explicitly checking for score > 0 to avoid counting 0 scores as valid
                  const isPositive = hasNumericValue && parseFloat(score.score) > 0;

                  // A score is only valid if it meets all criteria
                  const hasValidScore = isCurrentJudge && hasNumericValue && isPositive;

                  if (hasValidScore) {
                    console.log(`Group ${group._id} in classification ${classification.id?._id} has valid score: ${score.score} (${typeof score.score})`);
                  } else if (isCurrentJudge) {
                    let reason = "";
                    if (!hasNumericValue) {
                      reason = "Not a valid number";
                    } else if (!isPositive) {
                      reason = "Score is not greater than 0";
                    }
                    console.log(`Group ${group._id} in classification ${classification.id?._id} has INVALID score: ${score.score} (${typeof score.score}), reason: ${reason}, value: ${JSON.stringify(score)}`);
                  }

                  return hasValidScore;
                });
            }

            if (!isScored) {
              console.log(`Group ${group._id} has NO valid score`);
            }

            return isScored;
          }).length : 0;

          console.log(`Classification ${classification.id?.name}: ${scoredGroups}/${totalGroups} groups scored`);

          // Determine if this classification is fully scored
          // Only consider it fully scored if there are groups and all of them have been scored
          const isFullyScored = totalGroups > 0 && scoredGroups > 0 && scoredGroups === totalGroups;

          return {
            id: classification.id?._id || 'unknown',
            name: classification.id?.name || 'Unknown Classification',
            phase: classification.phase || 'Not specified',
            totalGroups,
            scoredGroups,
            unscoredGroups: totalGroups - scoredGroups,
            progress: totalGroups > 0 ? (scoredGroups / totalGroups) * 100 : 0,
            isFullyScored
          };
        }) || [];

        // Calculate overall stats for this category
        const totalClassifications = classificationsData.length;

        // A classification is only counted as completed if it has groups and all of them are scored
        const completedClassifications = classificationsData.filter(c =>
          c.totalGroups > 0 && c.isFullyScored
        ).length;

        const remainingClassifications = totalClassifications - completedClassifications;

        // Calculate overall progress based on classifications
        const classificationProgress = totalClassifications > 0
          ? (completedClassifications / totalClassifications) * 100
          : 0;

        return {
          id: categoryId || 'unknown',
          name: categoryName || 'Unknown Category',
          classifications: classificationsData,
          totalClassifications,
          completedClassifications,
          remainingClassifications,
          progress: classificationProgress,
          // Keep group stats for detailed view
          totalGroups: classificationsData.reduce((sum, c) => sum + c.totalGroups, 0),
          scoredGroups: classificationsData.reduce((sum, c) => sum + c.scoredGroups, 0),
          unscoredGroups: classificationsData.reduce((sum, c) => sum + c.unscoredGroups, 0)
        };
      });

      setSummaryData(processedData);
    }
  }, [assignments, judge]);

  // Calculate overall judging progress
  const calculateOverallProgress = () => {
    if (!summaryData || summaryData.length === 0) return { progress: 0, total: 0, completed: 0 };

    // Count total classifications that have groups (we only care about classifications with groups)
    const totalClassifications = summaryData.reduce((sum, category) => {
      // Count classifications that have at least one group
      const classificationsWithGroups = category.classifications.filter(c => c.totalGroups > 0).length;
      return sum + classificationsWithGroups;
    }, 0);

    const completedClassifications = summaryData.reduce((sum, category) => sum + category.completedClassifications, 0);
    const progress = totalClassifications > 0 ? (completedClassifications / totalClassifications) * 100 : 0;

    console.log(`Overall progress: ${completedClassifications}/${totalClassifications} classifications completed (${Math.round(progress)}%)`);

    return {
      progress,
      total: totalClassifications,
      completed: completedClassifications,
      remaining: totalClassifications - completedClassifications
    };
  };

  const overallProgress = calculateOverallProgress();



  // Render the appropriate view based on currentView state
  return (
    <>
      {currentView === 'summary' && (
        <SummaryView
          judge={judge}
          summaryData={summaryData}
          overallProgress={overallProgress}
          onClassificationClick={handleClassificationClick}
        />
      )}

      {currentView === 'classificationSummary' && (
        <ClassificationSummaryView
          selectedClassification={selectedClassification}
          processedGroups={sortedGroups.length > 0 ? sortedGroups : processedGroups}
          isAutoSelectLoading={isAutoSelectLoading}
          handleAutoSelectTopGroups={handleAutoSelectTopGroups}
          handleMovesOnChange={handleMovesOnChange}
          handleNationalChange={handleNationalChange}
          handleCitationChange={handleCitationChange}
          handleCommendedChange={handleCommendedChange}
          handleAutoSelectNational={handleAutoSelectNational}
          handleAutoSelectCitation={handleAutoSelectCitation}
          handleAutoSelectCommended={handleAutoSelectCommended}
          handleGroupSelect={handleGroupSelect}
          isCitationCategory={AppStore.currentSelectedCategory?.name?.toLowerCase().includes('citation') || false}
          onBackToSummary={() => {
            setCurrentView('summary');
            // Clear the selected category and classification in AppStore
            AppStore.setCurrentSelectedCategory(null);
            AppStore.setCurrentSelectedClassification(null);
          }}
          onViewAllGroups={() => setCurrentView('groups')}
        />
      )}

      {currentView === 'groups' && (
        <GroupsView
          selectedClassification={selectedClassification}
          processedGroups={sortedGroups.length > 0 ? sortedGroups : processedGroups}
          sortConfig={sortConfig}
          requestSort={requestSort}
          isAutoSelectLoading={isAutoSelectLoading}
          handleAutoSelectTopGroups={handleAutoSelectTopGroups}
          handleMovesOnChange={handleMovesOnChange}
          handleNationalChange={handleNationalChange}
          handleCitationChange={handleCitationChange}
          handleCommendedChange={handleCommendedChange}
          handleAutoSelectNational={handleAutoSelectNational}
          handleAutoSelectCitation={handleAutoSelectCitation}
          handleAutoSelectCommended={handleAutoSelectCommended}
          handleGroupSelect={handleGroupSelect}
          isCitationCategory={AppStore.currentSelectedCategory?.name?.toLowerCase().includes('citation') || false}
          onBackToClassificationSummary={() => setCurrentView('classificationSummary')}
          onBackToSummary={() => {
            setCurrentView('summary');
            // Clear the selected category and classification in AppStore
            AppStore.setCurrentSelectedCategory(null);
            AppStore.setCurrentSelectedClassification(null);
          }}
        />
      )}
    </>
  );
};

export default observer(JudgeSummary);
