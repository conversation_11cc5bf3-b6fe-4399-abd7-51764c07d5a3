import { useState, useEffect, useContext, useCallback, useRef, Fragment } from "react";
import Webcam from "react-webcam";
import { BsRecord2 } from "react-icons/bs";
import { FiSave, FiChevronRight, FiPlay, FiPause } from "react-icons/fi";
import { FaStop } from "react-icons/fa";
import { observer } from "mobx-react";
import { AppProviderStore } from "../../AppStore";
import { toJS } from "mobx";
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

function VideoRecorder({ judge, category, handleVideoSubmit }) {
  const { AppStore } = useContext(AppProviderStore);

  console.log("videos", toJS(judge));
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const [capturing, setCapturing] = useState(false);
  const [tempURL, setTempURL] = useState(null);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [isPlayingPreview, setIsPlayingPreview] = useState(false);

  useEffect(() => {
    return () => {
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== "inactive") {
        mediaRecorderRef.current.removeEventListener("dataavailable", handleDataAvailable);
        mediaRecorderRef.current.stop();
      }
    };
  }, []);
  
  const handleRerecordClick = useCallback(() => {
    console.log('rerecord')

    // Clean up existing tempURL to prevent memory leaks
    if (tempURL) {
      URL.revokeObjectURL(tempURL);
      setTempURL(null);
    }

    // Reset recorded chunks
    setRecordedChunks([]);

    // Reset judge intro if it exists
    if (judge.judgeIntro) {
      judge.judgeIntro = null;
    }

    // Reset preview state
    setIsPlayingPreview(false);
  }, [tempURL, judge]);

  const handleDataAvailable = useCallback(
    ({ data }) => {
      if (data.size > 0) {
        setRecordedChunks((prev) => prev.concat(data));
      }
    },
    [setRecordedChunks]
  );

  const handleStopCaptureClick = useCallback(() => {
    console.log('handleStop')
    mediaRecorderRef.current.stop();
    setCapturing(false);
  }, [mediaRecorderRef, setCapturing]);

  // Create tempURL when recordedChunks are updated
  useEffect(() => {
    if (recordedChunks.length > 0 && !capturing) {
      console.log('Creating tempURL from recorded chunks:', recordedChunks.length);
      const supportedMimeType = MediaRecorder.isTypeSupported("video/webm")
        ? "video/webm"
        : "video/mp4";

      const blob = new Blob(recordedChunks, {
        type: supportedMimeType,
      });

      const url = URL.createObjectURL(blob);
      console.log('Setting tempURL:', url);
      setTempURL(url);

      // Clean up previous URL to prevent memory leaks
      return () => {
        if (tempURL) {
          URL.revokeObjectURL(tempURL);
        }
      };
    }
  }, [recordedChunks, capturing]);

  const handleSave = useCallback(async () => {
    console.log('handleSave clicked')
    const supportedMimeType = MediaRecorder.isTypeSupported("video/webm")
      ? "video/webm"
      : "video/mp4"; // Fallback to a different MIME type if webm is not supported
  
    if (recordedChunks.length) {
      const blob = new Blob(recordedChunks, {
        type: supportedMimeType,
      });
      console.log('handleSave', supportedMimeType)
      let fileExtension = supportedMimeType.includes("webm") ? "webm" : "mp4"
      let fileName = `${uuidv4()}.${fileExtension}`
      let year = new Date().getFullYear()
      let fullPath = `${year}/judges/${AppStore.judge.name}/${fileName}`
      let presignedRes = await axios.post(`/api/minioPresignedUrl`,{filename: fullPath})
      let presignedUrl = presignedRes.data.presigned
      let minioRes = await axios.put(presignedUrl,blob);
      let uri = `https://app.foundationformusiceducation.org/fme-app/${fullPath}`
      // let uri = await AppStore.saveBlob(blob, fileExtension);
      
      await AppStore.saveVideo(uri);

      // Clean up tempURL after saving
      if (tempURL) {
        URL.revokeObjectURL(tempURL);
        setTempURL(null);
      }

      setRecordedChunks([]);
      setIsPlayingPreview(false);
      AppStore.setJudgeVideoCreated(true);
    }
  }, [recordedChunks, tempURL]);
  
  const handleStartCaptureClick = useCallback(() => {
    console.log('handleStartCapture')
    setCapturing(true);
    const supportedMimeType = MediaRecorder.isTypeSupported("video/webm")
      ? "video/webm"
      : "video/mp4"; // Fallback to a different MIME type if webm is not supported
  
    if (!MediaRecorder.isTypeSupported(supportedMimeType)) {
      // Handle the case where neither webm nor mp4 are supported
      console.error("WebM or MP4 is not supported");
      return;
    }
  
    try {
      mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {
        mimeType: supportedMimeType,
      });
      mediaRecorderRef.current.addEventListener("dataavailable", handleDataAvailable);
      mediaRecorderRef.current.start();
  
      // Set a timeout to stop capturing after 60 seconds
      setTimeout(() => {
        handleStopCaptureClick();
      }, 60000);
    } catch (error) {
      console.error("Failed to create MediaRecorder:", error);
      // Handle the error accordingly (e.g., show a user-friendly message)
    }
  }, [webcamRef, setCapturing, mediaRecorderRef, handleDataAvailable, handleStopCaptureClick]);



  const [deviceId, setDeviceId] = useState({});
  const [devices, setDevices] = useState([]);

  const handleDevices = useCallback(
    (mediaDevices) =>
      setDevices(mediaDevices.filter(({ kind }) => kind === "videoinput")),
    [setDevices]
  );

  useEffect(() => {
    navigator.mediaDevices.enumerateDevices().then(handleDevices);
  }, [handleDevices]);

  return (
    <>
      {judge && (
        <div>
          {
            //we will probably have to make this look good
            // devices.map((device, key) => (
            //       <div>
            //         <h1>Choose Device</h1>
            //         <Webcam audio={false} videoConstraints={{ deviceId: device.deviceId }} />
            //         {device.label || `Device ${key + 1}`}
            //       </div>
            //   ))
          }

          <div className="">
          <h1 className="text-[#669f9d]">
              Welcome <strong className="text-white">{judge.name}</strong>, thank you
              for being<br></br> an adjudicator for the Mark of Excellence competition. 
            </h1>
            <p className="text-[#669f9d]">Please record a brief introduction</p>
          </div>
          <div className="border-8 border-white rounded-2xl bg-[#eeeeee] mt-3">
            {!judge.judgeIntro && !tempURL || capturing ? (
              <Webcam muted={true} audio={true} ref={webcamRef} />
            ) : (
              judge.judgeIntro ? (
                <div className="relative">
                  {isPlayingPreview ? (
                    <video
                      controls
                      src={judge.judgeIntro}
                      className="w-full h-auto"
                      style={{ maxHeight: '400px' }}
                      autoPlay
                      onEnded={() => setIsPlayingPreview(false)}
                    />
                  ) : (
                    <>
                      <video
                        src={judge.judgeIntro}
                        className="w-full h-auto"
                        style={{ maxHeight: '400px' }}
                        poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='.3em' font-family='Arial, sans-serif' font-size='14' fill='%23666'%3EClick to play%3C/text%3E%3C/svg%3E"
                      />
                      {/* Play overlay */}
                      <div
                        className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 cursor-pointer hover:bg-opacity-50 transition-all duration-200 rounded-lg"
                        onClick={() => setIsPlayingPreview(true)}
                      >
                        <FiPlay className="text-white text-6xl" />
                      </div>
                    </>
                  )}
                  <div className="absolute top-2 right-2 bg-green-600 text-white px-2 py-1 rounded text-sm">
                    Saved
                  </div>
                </div>
              ) : (
                <div className="relative">
                  {isPlayingPreview ? (
                    <video
                      controls
                      src={tempURL}
                      className="w-full h-auto"
                      style={{ maxHeight: '400px' }}
                      autoPlay
                      onEnded={() => setIsPlayingPreview(false)}
                    />
                  ) : (
                    <>
                      <video
                        src={tempURL}
                        className="w-full h-auto"
                        style={{ maxHeight: '400px' }}
                        poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='.3em' font-family='Arial, sans-serif' font-size='14' fill='%23666'%3EClick to play%3C/text%3E%3C/svg%3E"
                      />
                      {/* Play overlay */}
                      <div
                        className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 cursor-pointer hover:bg-opacity-50 transition-all duration-200 rounded-lg"
                        onClick={() => setIsPlayingPreview(true)}
                      >
                        <FiPlay className="text-white text-6xl" />
                      </div>
                    </>
                  )}
                  <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-sm">
                    Preview - Not Saved
                  </div>
                </div>
              )
            )}
          </div>
          <div className="mt-5 flex items-center justify-between">
            {capturing ? (
              <button
                onClick={handleStopCaptureClick}
                className="bg-[#728ab6] px-3 py-2 text-white flex items-center"
              >
                Stop &nbsp;&nbsp; <FaStop color="black" size={16} />
              </button>
            ) : (
              <>
                {judge.judgeIntro || tempURL ? (
                  <Fragment>
                  {/* Preview button - only show when there's a video to preview */}
                  {(judge.judgeIntro || tempURL) && (
                    <button
                      onClick={() => setIsPlayingPreview(!isPlayingPreview)}
                      className="bg-[#3b8c6e] px-3 py-2 text-white flex items-center rounded hover:bg-[#2a6b4f] transition-colors"
                    >
                      {isPlayingPreview ? (
                        <>Stop Preview <FiPause color="white" size={20} /></>
                      ) : (
                        <>Preview Video <FiPlay color="white" size={20} /></>
                      )}
                    </button>
                  )}

                  <button
                    onClick={handleRerecordClick}
                    className="bg-[#728ab6] px-3 py-2 text-white flex items-center rounded hover:bg-[#5a6b8a] transition-colors"
                  >
                    Rerecord <BsRecord2 color="red" size={20} />
                  </button>

                  {judge.judgeIntro ? (
                    <button
                      onClick={()=>{AppStore.setJudgeVideoCreated(true)}}
                      className="bg-[#009571] flex items-center px-3 py-2 text-white rounded hover:bg-[#007a5a] transition-colors"
                    >
                      Continue&nbsp; <FiChevronRight color="white" size={20} />
                    </button>
                  ) : (
                    <div className="flex gap-2">
                      <button
                        onClick={()=>{AppStore.setJudgeVideoCreated(true)}}
                        className="bg-gray-500 flex items-center px-3 py-2 text-white rounded hover:bg-gray-600 transition-colors"
                      >
                        Skip for Now&nbsp; <FiChevronRight color="white" size={20} />
                      </button>
                    </div>
                  )}
                  </Fragment>
                ) : (
                  <button
                    onClick={handleStartCaptureClick}
                    className="bg-[#728ab6] px-3 py-2 text-white flex items-center"
                  >
                    Start <BsRecord2 color="red" size={20} />
                  </button>
                )}
              </>
            )}
            {recordedChunks.length > 0 && !judge.judgeIntro && (
              <button
                onClick={handleSave}
                className="bg-[#009571] flex items-center px-3 py-2 text-white rounded hover:bg-[#007a5a] transition-colors font-semibold"
              >
                Save Video&nbsp; <FiSave color="white" size={20} />
              </button>
            )}
          </div>
        </div>
      )}
    </>
  );
}

export default observer(VideoRecorder);
