import { action, observable, toJS, autorun, runInAction } from "mobx";
import React, { createContext } from "react";
import axios from "axios";
import gravatarUrl from "gravatar-url";
import { jwtDecode } from "jwt-decode";
import { genDataGridProps } from "./lib/tableHelper.mjs";

let appStore = observable(
  {
    appName: "FME App",
    selectedGroup: {
      trackList: [],
    },
    selectedAssignment: null,
    menuSelected: null,
    judge: null,
    user: null,
    users: null,
    categoryId: null,
    category: null,
    queryParameters: null,
    classifications: null,
    judgeVideoCreated: false,
    videoSubmitted: false,
    isBanner: true,
    trackList: [],
    currentTrack: null,
    ensemble: {
      ensembleName: "Default Ensemble",
    },
    groups: [],
    contests: [],
    categories: [],
    judges: [],
    assignments: [],
    judgeIntros: [],
    isRecording: false,
    isSavingMemo: false,
    competitionState: {},
    formOptions: {},
    drawer: false,
    score: null,
    wpToken: null,
    showAdmin: false,
    selectedClassificationIndex: -1,
    currentSelectedCategory: null,
    currentSelectedClassification: null,
    isDev: process.env.REACT_APP_ENV === "dev",

    get memos() {
      if (appStore.currentTrack && appStore.currentTrack.memos) {
        return appStore.currentTrack.memos;
      } else {
        console.log("Error AppStore.js get memos(), memos not defined");
      }
    },
    get score() {
      if (appStore.selectedGroup.score > 0) {
        return appStore.selectedGroup.score;
      } else {
        return "";
      }
    },
    get trackList() {
      return appStore.selectedGroup.tracks;
    },
    get judge_profile_img() {
      // console.log('appStore judge_profile_img()')
      // console.log(toJS(appStore.judge))
      return gravatarUrl(appStore.judge.email, { size: 200 });
    },
    setJudgeScore: function (score) {
      console.log("Setting judge score:", toJS(score));
      appStore.judgeScore = {
        judgeId: appStore.judge._id,
        score: score.score !== undefined ? Number(score.score) : null,
        movesOn: Boolean(score.movesOn),
        isCommended: Boolean(score.isCommended),
        isNational: Boolean(score.isNational),
        isCitation: Boolean(score.isCitation),
        isState: Boolean(score.isState),
      };
    },
    handleScoreUpdate: async function (value, field) {
      const updatedScore = {
        ...appStore.judgeScore,
        [field]: field === "score" ? Number(value) : Boolean(value),
      };

      appStore.setJudgeScore(updatedScore);

      // Automatically save score after update
      const updatedGroup = await appStore.setGroupScore();

      // If we got an updated group from the server, use it to update the UI
      if (updatedGroup) {
        // Update the score in the assignments structure for the sidebar display
        if (appStore.assignments && appStore.assignments.length > 0) {
          for (const assignment of appStore.assignments) {
            if (
              assignment.classifications &&
              assignment.classifications.length > 0
            ) {
              for (const classification of assignment.classifications) {
                if (classification.groups && classification.groups.length > 0) {
                  const groupIndex = classification.groups.findIndex(
                    (g) => g._id === updatedGroup._id
                  );
                  if (groupIndex !== -1) {
                    // Replace with the updated group
                    classification.groups[groupIndex] = JSON.parse(
                      JSON.stringify(updatedGroup)
                    );
                    console.log(
                      "Updated group in assignments structure from handleScoreUpdate"
                    );
                  }
                }
              }
            }
          }
        }

        // Update the group in the main groups array if it exists there
        const groupIndex = appStore.groups.findIndex(
          (g) => g._id === updatedGroup._id
        );
        if (groupIndex !== -1) {
          // Make a deep copy of the updated group to ensure all references are updated
          appStore.groups[groupIndex] = JSON.parse(
            JSON.stringify(updatedGroup)
          );
          console.log(
            "Updated group in main groups array from handleScoreUpdate"
          );
        }
      }
    },
    setGroups: function (groups) {
      console.log("setGroups", toJS(groups));
      appStore.groups = groups;
    },
    setUser: function (user) {
      console.log("mobx", user);
      appStore.user = user;
      console.log(toJS(appStore.user));
    },
    getGroups: async function () {
      let res = await axios
        .get(`/api/groups`)
        .then((res) => {
          appStore.groups = res?.data; // set the state
        })
        .catch((err) => {
          console.log("err", err);
        });
      return res?.data;
    },
    getContests: async function () {
      let res = await axios
        .get(`/api/contests`)
        .then((res) => {
          appStore.contests = res?.data; // set the state
        })
        .catch((err) => {
          console.log("err");
        });
      return res?.data;
    },
    getCategories: async function () {
      let res = await axios
        .get("/api/categories")
        .then((res) => {
          appStore.categories = res?.data;
        })
        .catch((err) => {
          console.log(err);
        });
      return res?.data;
    },
    getJudges: async function () {
      console.log("gettingJudges");
      let res = await axios
        .get(`/api/judges`)
        .then((res) => {
          console.log("res", res);
          appStore.judges = res?.data; // set the state
        })
        .catch((err) => {
          console.log("err");
        });
      return res?.data;
    },
    addJudge: async function (judgeData) {
      try {
        // Format the judge data
        const formattedJudge = {
          name: judgeData.name,
          email: judgeData.email,
          assigned: judgeData.assigned.map((assignment) => ({
            category: assignment.category,
            classifications: assignment.classifications.map(
              (classification) => {
                // Start with basic classification data
                const formattedClassification = {
                  id: classification.id,
                };

                // Only add phase if it exists
                if (classification.phase) {
                  formattedClassification.phase = classification.phase;
                }

                // Only add groups if they exist and are not empty
                if (classification.groups && classification.groups.length > 0) {
                  formattedClassification.groups = classification.groups;
                }

                return formattedClassification;
              }
            ),
          })),
          isActive: true,
        };

        const res = await axios.post(`/api/judges/add`, [formattedJudge]);
        if (res.status === 201) {
          console.log("Judge added successfully", res.data);
          // Update the appStore with the new judges
          runInAction(() => {
            if (!appStore.judges) {
              appStore.judges = [];
            }
            appStore.judges = [...appStore.judges, ...res.data.judges];
          });
          return true;
        } else {
          console.log("Failed to add judge", res);
          return false;
        }
      } catch (err) {
        console.log("Error adding judge", err);
        return false;
      }
    },
    updateJudge: async function (judgeId, judgeData) {
      try {
        // Format the judge data
        const formattedJudge = {
          name: judgeData.name,
          email: judgeData.email,
          assigned: judgeData.assigned.map((assignment) => ({
            category: assignment.category,
            classifications: assignment.classifications.map(
              (classification) => {
                // Start with basic classification data
                const formattedClassification = {
                  id: classification.id,
                };

                // Only add phase if it exists
                if (classification.phase) {
                  formattedClassification.phase = classification.phase;
                }

                // Only add groups if they exist and are not empty
                if (classification.groups && classification.groups.length > 0) {
                  formattedClassification.groups = classification.groups;
                }

                return formattedClassification;
              }
            ),
          })),
        };

        const res = await axios.put(
          `/api/judges/edit/${judgeId}`,
          formattedJudge
        );
        if (res.status === 200) {
          // Update the judge in the local store using runInAction
          runInAction(() => {
            const index = appStore.judges.findIndex(
              (judge) => judge._id === judgeId
            );
            if (index !== -1) {
              appStore.judges[index] = res.data.judge;
              // Create a new array reference to trigger updates
              appStore.judges = [...appStore.judges];
            }
          });
          return true;
        }
        return false;
      } catch (err) {
        console.log("Error updating judge:", err);
        return false;
      }
    },
    removeJudge: async function (judgeId) {
      try {
        const res = await axios.put(`/api/judges/remove/${judgeId}`);
        if (res.status === 200) {
          // Remove judge from local state using runInAction
          runInAction(() => {
            appStore.judges = appStore.judges.filter(
              (judge) => judge._id !== judgeId
            );
          });
          return true;
        }
        return false;
      } catch (err) {
        console.log("Error removing judge:", err);
        return false;
      }
    },
    getUsers: async function () {
      console.log("getting users");
      let res = await axios
        .get(`/api/getAllUsers`)
        .then((res) => {
          console.log(res);
          appStore.users = res?.data;
        })
        .catch((err) => {
          console.log("err getting users: ", err);
        });
      return res?.data;
    },
    setGroup: function (group) {
      console.log("AppStore.setGroup called with group ID:", group._id);
      runInAction(() => {
        let toSet = group;
        if (toSet?.judgeIntros) {
          toSet = group.group;
          appStore.setJudgeIntros(group.judgeIntros);
        }
        appStore.selectedGroup = toSet;
        appStore.currentTrack = toSet.tracks[0];
        console.log("AppStore.selectedGroup set to:", appStore.selectedGroup._id);
      });
    },
    updateSelectedGroup: async function (groupUpdate) {
      let res = await axios.put(`/api/groups/adminUpdate`, groupUpdate);
      if (res.data) {
        appStore.selectedGroup = res.data;
        const groupIndex = appStore.groups.findIndex(
          (group) => group.group._id === res.data._id
        );
        console.log("appstore", groupIndex);
        if (groupIndex > -1) {
          appStore.groups[groupIndex].group = res.data;
          // console.log('appstore', toJS(appStore.groups[groupIndex]), res.data)
        }
        return true;
      } else {
        return false;
      }
    },
    setAssignments: function (assignments) {
      console.log("setting assignments", assignments);
      appStore.assignments = assignments;
    },
    setJudgeIntros: function (intros) {
      appStore.judgeIntros = intros;
    },
    unselectGroup: function () {
      console.log("AppStore: Resetting selected group");
      // Reset to an empty object with trackList to match the initial state
      appStore.selectedGroup = {
        trackList: [],
      };
      // Also reset the current track
      appStore.currentTrack = null;
    },
    setSelectedMenu: function (option) {
      appStore.menuSelected = option;
    },
    setIsRecording: function () {
      appStore.isRecording = !appStore.isRecording;
    },
    setIsSavingMemo: function (isSaving) {
      appStore.isSavingMemo = isSaving;
    },
    addAudioMemo: function (memo, previous) {
      console.log("Adding Audio Memo", memo, previous);
      if (previous) {
        console.log("changed track while recording");
      }
      let memoObj = {
        judge: appStore.judge._id,
        content: memo,
      };
      appStore.memos.audioMemos.push(memoObj);
      appStore.saveTrack();
      //this is a good place to update the memo
    },
    deleteAudioMemo: function (memoToDelete) {
      // Filter out the memo to delete based on judge and content
      appStore.memos.audioMemos = appStore.memos.audioMemos.filter(
        (memo) =>
          memo.judge.toString() !== memoToDelete.judge.toString() ||
          memo.content !== memoToDelete.content
      );

      // Extract the minioPath from memoToDelete's content
      let index = memoToDelete.content.indexOf("fme-app/");
      let minioPath = memoToDelete.content.substring(index + "fme-app/".length);

      // Send a request to remove the Minio object using axios
      axios
        .post(`/api/removeMinioObject`, { filename: minioPath })
        .then((response) => {
          // Handle success response if needed
          console.log("Minio object removed:", response.data);
        })
        .catch((error) => {
          // Handle error response if needed
          console.error("Error removing Minio object:", error);
        });

      // Save the updated track in appStore
      appStore.saveTrack();
    },
    setPerformanceNotes: function (note) {
      appStore.memos.notes = note;
    },
    setSoloistNotes: function (note) {
      appStore.memos.soloistNotes = note;
    },
    setGroupScore: async function () {
      if (!appStore.selectedGroup || !appStore.judgeScore) return;

      try {
        const payload = {
          groupId: appStore.selectedGroup._id,
          judgeId: appStore.judge._id,
          score: appStore.judgeScore.score,
          movesOn: appStore.judgeScore.movesOn,
          isCommended: appStore.judgeScore.isCommended,
          isNational: appStore.judgeScore.isNational,
          isCitation: appStore.judgeScore.isCitation,
          isState: appStore.judgeScore.isState,
        };

        // Add classification index if it exists
        if (appStore.selectedClassificationIndex !== -1) {
          payload.classificationIndex = appStore.selectedClassificationIndex;
        }

        console.log("Sending score update payload:", payload);

        const response = await fetch("/api/groups/updateGroupScore", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          throw new Error("Failed to update score");
        }

        const updatedGroup = await response.json();
        console.log("Received updated group:", updatedGroup);

        // Update the selectedGroup with the fresh data from the server
        appStore.selectedGroup = updatedGroup;

        // Find and update the group in the assignments structure
        // This ensures the sidebar displays the correct data
        if (appStore.assignments && appStore.assignments.length > 0) {
          for (const assignment of appStore.assignments) {
            if (
              assignment.classifications &&
              assignment.classifications.length > 0
            ) {
              for (const classification of assignment.classifications) {
                if (classification.groups && classification.groups.length > 0) {
                  const groupIndex = classification.groups.findIndex(
                    (g) => g._id === updatedGroup._id
                  );
                  if (groupIndex !== -1) {
                    // Replace with the updated group
                    classification.groups[groupIndex] = updatedGroup;
                    console.log("Updated group in assignments structure");
                  }
                }
              }
            }
          }
        }

        // Update the group in the main groups array if it exists there
        const groupIndex = appStore.groups.findIndex(
          (g) => g._id === updatedGroup._id
        );
        if (groupIndex !== -1) {
          // Make a deep copy of the updated group to ensure all references are updated
          appStore.groups[groupIndex] = JSON.parse(
            JSON.stringify(updatedGroup)
          );
          console.log("Updated group in main groups array");
        }

        // Re-load the judge score from the updated group
        if (appStore.selectedClassificationIndex !== -1) {
          const classification =
            updatedGroup.classifications[appStore.selectedClassificationIndex];
          if (
            classification &&
            classification.judgeScores &&
            Array.isArray(classification.judgeScores)
          ) {
            const updatedScore = classification.judgeScores.find(
              (score) => score.judgeId === appStore.judge._id
            );
            if (updatedScore) {
              console.log(
                "Setting judge score from classification:",
                updatedScore
              );
              appStore.setJudgeScore(updatedScore);
            }
          }
        } else {
          if (
            updatedGroup.judgeScores &&
            Array.isArray(updatedGroup.judgeScores)
          ) {
            const updatedScore = updatedGroup.judgeScores.find(
              (score) => score.judgeId === appStore.judge._id
            );
            if (updatedScore) {
              console.log("Setting judge score from group:", updatedScore);
              appStore.setJudgeScore(updatedScore);
            }
          }
        }

        // Force a refresh of the UI by triggering a state update
        const currentGroups = [...appStore.groups];
        appStore.setGroups(currentGroups);

        return updatedGroup;
      } catch (error) {
        console.error("Error updating score:", error);
        return null;
      }
    },
    saveGroupScore: function () {
      let body = {
        groupId: appStore.selectedGroup._id,
        ...appStore.judgeScore,
      };
      console.log("body", toJS(body));
      axios.put("/api/groups/updateGroupScore", body);
    },
    setOldGroupScore: function () {
      appStore.selectedGroup.score = score;
      appStore.saveOldGroupScore(score);
    },
    saveOldGroupScore: function () {
      axios.post("/api/groups/updateOldGroupScore", appStore.selectedGroup);
    },

    saveTrack: function () {
      axios.post("/api/tracks/updateTrack", appStore.currentTrack);
    },
    updateCurrentTrack: async function (trackUpdate) {
      let res = await axios.put(`/api/tracks/adminUpdate`, trackUpdate);
      if (res.data) {
        appStore.currentTrack = res.data;
        const trackIndex = appStore.trackList.findIndex(
          (track) => track._id === res.data._id
        );
        if (trackIndex > -1) {
          appStore.trackList[trackIndex] = res.data;
        }
        return true;
      } else {
        return false;
      }
    },
    setTrack: function (track) {
      //alert("changing track");
      appStore.currentTrack = track;
    },
    setJudge: async function (judge) {
      console.log("setting judge (appstore): ", judge);
      appStore.judge = judge;
    },
    setCategoryId: function (categoryId) {
      appStore.categoryId = categoryId;
    },
    setCategory: function (category) {
      appStore.category = category;
    },
    setEnsemble: function (ensemble) {
      appStore.ensemble = ensemble;
    },
    setClassifications: function (classification) {
      appStore.classifications = classification;
    },
    setCurrentSelectedCategory: function (category) {
      console.log("Setting current selected category:", category);
      appStore.currentSelectedCategory = category;
    },
    setCurrentSelectedClassification: function (classification) {
      console.log("Setting current selected classification:", classification);
      appStore.currentSelectedClassification = classification;
    },
    setJudgeVideoCreated: function (judgeVideoCreated) {
      appStore.judgeVideoCreated = judgeVideoCreated;
    },
    saveVideo: function (uri) {
      let body = {
        uri: uri,
      };
      if (appStore.judge.assigned.length) {
        body.index = appStore.judge.assigned.indexOf(
          appStore.judge.assigned.find(
            (obj) => obj.category === appStore.categoryId
          )
        );
      }
      axios.post(`/api/judges/${appStore.judge._id}/judgeIntro`, body);
      appStore.judge.judgeIntro = uri;
    },
    setVideoSubmitted: function (videoSubmitted) {
      appStore.videoSubmitted = videoSubmitted;
    },
    setIsBanner: function (isBanner) {
      appStore.isBanner = isBanner;
    },
    saveBlob: function (blob_file, filetype) {
      return new Promise((resolve, reject) => {
        const theBlob = blob_file;
        var formData = new FormData();
        theBlob.lastModifiedDate = new Date();
        formData.append("file", theBlob);
        axios.get(`/api/presignedUrl?filetype=${filetype}`).then((response) => {
          console.log("presigned method");

          let presignedUrl = response.data.presigned;
          console.group(presignedUrl);
          let returnUrl = `https://app.foundationformusiceducation.org/fme-app/${response.data.filename}`;
          axios.put(presignedUrl, theBlob).then((response) => {
            if (response.status === 200) {
              resolve(returnUrl);
            }
          });
        });
        // axios.post(`/api/upload?filetype=${filetype}`, formData, {
        // 	headers: {
        // 		'Content-Type': 'multipart/form-data'
        // 	}
        // }).then((res) => {
        // 	let uri = res.data;
        // 	resolve(uri);
        // })
      });
    },

    getCompetitionState: async function () {
      let res = await axios
        .get(`/api/competitionState`)
        .then((res) => {
          console.log(res.data);
          appStore.competitionState = res?.data; // set the state
        })
        .catch((err) => {
          console.log("err");
        });
      return res?.data;
    },
    setCompetitionState: async function (toToggle) {
      appStore.competitionState[toToggle] =
        !appStore.competitionState[toToggle];
      await axios.put(`/api/competitionState/${toToggle}`);
    },
    setCompetitionMessage: async function (key, message) {
      appStore.competitionState[key] = message;
      await axios.put(`/api/competitionState/${key}`, { message: message });
    },
    getFormOptions: async function () {
      let formOptions = await axios.get(`/api/contests/populateAll`);
      appStore.formOptions = formOptions.data;
    },
    toggleDrawer: function () {
      appStore.drawer = !appStore.drawer;
    },
    toggleShowAdmin() {
      appStore.showAdmin = !appStore.showAdmin;
    },
    setShowAdmin(value) {
      appStore.showAdmin = value;
    },
    setToken: function (token) {
      appStore.wpToken = token;
    },
    fetchInvoiceDetails: async function (invoiceId) {
      let invoice = await axios.get(`/api/invoice/${invoiceId}`);
      console.log(invoice);
      return invoice.data;
    },
    reset: function () {
      appStore.user = null;
      appStore.selectedGroup = {}; // Ensure tracks are reset here
      appStore.currentTrack = null;
      appStore.judgeIntros = [];
      appStore.groups = [];
      appStore.contests = [];
      appStore.categories = [];
      appStore.judges = [];
      appStore.assignments = null;
      appStore.users = null;
      appStore.categoryId = null;
      appStore.category = null;
      appStore.queryParameters = null;
      appStore.classifications = null;
      appStore.judgeVideoCreated = false;
      appStore.videoSubmitted = false;
      appStore.isBanner = true;
      appStore.ensemble = { ensembleName: "Default Ensemble" };
      appStore.isRecording = false;
      appStore.competitionState = {};
      appStore.formOptions = {};
      appStore.drawer = false;
      appStore.menuSelected = null;
      appStore.judge = null;
      appStore.judgeScore = null;
    },
  },
  {
    setEnsemble: action,
    setGroup: action,
    setGroups: action,
    setJudgeIntros: action,
    setSelectedMenu: action,
    getGroups: action,
    getCategories: action,
    getContests: action,
    getJudges: action,
    getUsers: action,
    getCompetitionState: action,
    getFormOptions: action,
    unselectGroup: action,
    setJudge: action,
    addJudge: action,
    updateJudge: action,
    removeJudge: action,
    setAssignments: action,
    setCategory: action,
    setCategoryId: action,
    setClassifications: action,
    setCurrentSelectedCategory: action,
    setCurrentSelectedClassification: action,
    setIsLoading: action,
    setIsRecording: action,
    setIsSavingMemo: action,
    setVideoSubmitted: action,
    setIsBanner: action,
    setCompetitionState: action,
    setCompetitionMessage: action,
    setTableData: action,
    toggleDrawer: action,
    updateCurrentTrack: action,
    updateSelectedGroup: action,
    setJudgeScore: action,
    toggleShowAdmin: action,
    fetchInvoiceDetails: action,
    reset: action,
  }
);
autorun(() => {
  console.log(
    "Selected Group:",
    toJS(appStore.selectedGroup),
    "Judge Intros:",
    toJS(appStore.judgeIntros)
  );
});
//If current track changes mobx will run this one
autorun(() => {
  console.log("Changing currentTrack", toJS(appStore.currentTrack));
});

//if memos changes mobx will run this one
autorun(() => {
  console.log("whats inside memos?", toJS(appStore.memos));
});
autorun(() => {
  appStore.getCompetitionState();
});
autorun(async () => {
  console.log("checking for token");
  const token = localStorage.getItem("token");
  if (token) {
    let expires = jwtDecode(token).exp;
    const currTime = Math.floor(Date.now() / 1000);
    if (expires < currTime) {
      console.log("in here");
      localStorage.clear();
      if (window.location.pathname !== "/results") {
        window.location.href = "/";
      }
    } else {
      let storedUser = jwtDecode(token).returnUser;
      console.log(storedUser);
      let userRes = await axios.get(`/api/getUser/${storedUser._id}`);
      let user = userRes.data;
      console.log("autorun mobx storeduser", storedUser);
      console.log("autonrun mobx user", user);
      appStore.user = user;
      if (user.isAdmin) {
        appStore.showAdmin = true;
      }
    }
  }
});

const AppProviderStore = createContext(appStore);
const { Provider } = AppProviderStore;

const AppProvider = ({ children }) => {
  return <Provider value={{ AppStore: appStore }}>{children}</Provider>;
};

export { AppProviderStore, AppProvider };
