
import { observer } from 'mobx-react';
import { Controller } from 'react-hook-form';
import { Button } from '@mui/material';
import Select, { components } from 'react-select';
import { useEffect, useState } from 'react';
import { toJS } from 'mobx';
import { fetchEntries } from '../../../../lib/api';

const CategoryAssignments = observer(({
    control,
    watch,
    setValue,
    categories,
    handleNextStep,
    handlePrevStep,
    isEdit,
    currentJudge,
}) => {
    // Store groups per category/classification combination
    const [groupsByClassification, setGroupsByClassification] = useState({});
    const [loadingStates, setLoadingStates] = useState({});
    // Track last clicked option for shift+click range selection
    const [lastClickedOption, setLastClickedOption] = useState({});
    // Track which dropdowns are currently open
    const [openDropdowns, setOpenDropdowns] = useState({});

    // Function to create a unique key for category/classification combination
    const getClassificationKey = (categoryId, classificationId) => {
        const catId = categoryId._id || categoryId;
        const classId = classificationId._id || classificationId;
        return `${catId}-${classId}`;
    };

    // Function to handle range selection with shift+click
    const handleGroupSelection = (selectedOption, currentSelection, allOptions, key, event) => {
        let newSelection = [...currentSelection];

        if (event && event.shiftKey && lastClickedOption[key]) {
            // Find indices of last clicked and current option
            const lastIndex = allOptions.findIndex(opt => opt._id === lastClickedOption[key]._id);
            const currentIndex = allOptions.findIndex(opt => opt._id === selectedOption._id);

            if (lastIndex !== -1 && currentIndex !== -1) {
                // Select all options between last clicked and current (inclusive)
                const startIndex = Math.min(lastIndex, currentIndex);
                const endIndex = Math.max(lastIndex, currentIndex);

                for (let i = startIndex; i <= endIndex; i++) {
                    const option = allOptions[i];
                    if (!newSelection.find(selected => selected._id === option._id)) {
                        newSelection.push(option);
                    }
                }
            }
        } else {
            // Normal click behavior - toggle selection
            const isSelected = newSelection.find(selected => selected._id === selectedOption._id);
            if (isSelected) {
                newSelection = newSelection.filter(selected => selected._id !== selectedOption._id);
            } else {
                newSelection.push(selectedOption);
            }
        }

        // Update last clicked option for this classification
        setLastClickedOption(prev => ({ ...prev, [key]: selectedOption }));

        return newSelection;
    };

    // Custom Option component to handle click events
    const CustomOption = (props) => {
        const { data, selectProps } = props;
        const { classificationKey, onOptionClick } = selectProps;

        return (
            <components.Option
                {...props}
                innerProps={{
                    ...props.innerProps,
                    onClick: (event) => {
                        // Don't prevent default or stop propagation - let react-select handle menu behavior
                        onOptionClick(data, event, classificationKey);
                    },
                }}
            />
        );
    };

    // Function to fetch groups for a specific category and classification
    const loadGroups = async (categoryId, classificationId) => {
        const key = getClassificationKey(categoryId, classificationId);
        console.log('Loading groups for:', { categoryId, classificationId, key });

        setLoadingStates(prev => ({ ...prev, [key]: true }));
        try {
            const currentYear = new Date().getFullYear();
            console.log('debugging', categoryId, classificationId, currentYear)
            const data = await fetchEntries(categoryId._id || categoryId, classificationId._id || classificationId, currentYear, false);

            const formattedGroups = data.groups.map(group => {
                // Get the maskedName either directly or from classifications
                let maskedName;
                if (group.maskedName) {
                    console.log('Using direct maskedName:', group.maskedName)
                    maskedName = group.maskedName;
                } else if (group.classifications && group.classifications.length > 0) {
                    console.log('Looking in classifications:', group.classifications)
                    console.log('Target classificationId:', classificationId)
                    const targetClassificationId = classificationId._id || classificationId;

                    const matchingClassification = group.classifications.find(
                        c => {
                            const classId = c.id?._id || c.id;
                            console.log('Comparing classification:', classId, 'with target:', targetClassificationId)
                            return classId?.toString() === targetClassificationId.toString()
                        }
                    );
                    console.log('Found matching classification:', matchingClassification)
                    maskedName = matchingClassification?.maskedName;
                }

                // Fallback to 'Unknown' if no maskedName found
                if (!maskedName) {
                    console.warn('No maskedName found for group:', group._id, 'using "Unknown"');
                    maskedName = 'Unknown';
                }

                return {
                    _id: group._id,
                    name: `Group ${maskedName}`,
                    maskedName: maskedName,
                    originalGroup: group // Keep reference to original group data for debugging
                };
            });

            // Sort groups by maskedName
            formattedGroups.sort((a, b) => {
                if (!a.maskedName && !b.maskedName) return 0;
                if (!a.maskedName) return 1;
                if (!b.maskedName) return -1;

                const lengthDiff = a.maskedName.length - b.maskedName.length;
                if (lengthDiff === 0) {
                    return a.maskedName.localeCompare(b.maskedName);
                }
                return lengthDiff;
            });

            setGroupsByClassification(prev => ({ ...prev, [key]: formattedGroups }));
        } catch (error) {
            console.error('Error loading groups:', error);
            setGroupsByClassification(prev => ({ ...prev, [key]: [] }));
        } finally {
            setLoadingStates(prev => ({ ...prev, [key]: false }));
        }
    };

    // Watch for changes in the entire form value
    useEffect(() => {
        const subscription = watch((formValues) => {
            if (!formValues?.assigned) return;

            formValues.assigned.forEach((categoryAssignment) => {
                if (!categoryAssignment?.classifications) return;

                categoryAssignment.classifications.forEach((classification) => {
                    if (categoryAssignment.category && classification?.id) {
                        console.log('Triggering loadGroups with:', {
                            category: categoryAssignment.category,
                            classification: classification.id
                        });
                        loadGroups(categoryAssignment.category, classification.id);
                    }
                });
            });
        });

        return () => subscription.unsubscribe();
    }, [watch]);

    // Load initial groups for existing assignments
    useEffect(() => {
        if (isEdit && currentJudge?.assigned) {
            currentJudge.assigned.forEach((categoryAssignment) => {
                if (categoryAssignment.classifications) {
                    categoryAssignment.classifications.forEach((classification) => {
                        if (categoryAssignment.category && classification.id) {
                            loadGroups(categoryAssignment.category, classification.id);
                        }
                    });
                }
            });
        }
    }, [isEdit, currentJudge]);

    useEffect(() => {
        console.log('Edit Mode:', isEdit);
        console.log('Current Judge:', toJS(currentJudge));

        if (isEdit && currentJudge?.assigned) {
            console.log('Setting assigned value:', currentJudge.assigned);

            // Set default value for the assigned field
            setValue('assigned', currentJudge.assigned, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true
            });
        } else {
            // Initialize with empty array if not editing
            setValue('assigned', [], {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true
            });
        }
    }, [isEdit, currentJudge, setValue]);

    // Add this to verify what's being watched
    const assignedValue = watch('assigned');
    console.log('Current form assigned value:', assignedValue);
    console.log('Groups by classification:', groupsByClassification);
    return (
        <div className="space-y-6">
            <Controller
                name="assigned"
                control={control}
                defaultValue={[]}
                render={({ field: { value = [], onChange } }) => (
                    <div>
                        {value.map((categoryAssignment, categoryIndex) => (
                            <div key={categoryIndex} className="mb-6 p-4 border rounded">
                                {/* Category Select */}
                                <div className="mb-4">
                                    <label className="block mb-2">Category</label>
                                    <Select
                                        value={(() => {
                                            const categoryId = categoryAssignment.category?._id || categoryAssignment.category;
                                            const foundCategory = categories.find(cat => cat._id === categoryId);
                                            console.log('Category dropdown - categoryAssignment.category:', categoryAssignment.category);
                                            console.log('Category dropdown - extracted categoryId:', categoryId);
                                            console.log('Category dropdown - foundCategory:', foundCategory);
                                            console.log('Category dropdown - available categories:', categories.map(c => ({ _id: c._id, name: c.name })));
                                            return foundCategory;
                                        })()}
                                        onChange={(selectedCategory) => {
                                            console.log('Category selected:', selectedCategory);
                                            const newValue = [...value];
                                            newValue[categoryIndex] = {
                                                category: selectedCategory._id,
                                                classifications: []
                                            };
                                            onChange(newValue);
                                        }}
                                        options={categories}
                                        getOptionLabel={(option) => option.name}
                                        getOptionValue={(option) => option._id}
                                        maxMenuHeight={200} // Makes the dropdown scrollable with a max height of 200px
                                        className="basic-select"
                                        classNamePrefix="select"
                                    />
                                </div>

                                {/* Classifications */}
                                {categoryAssignment.category && (
                                    <div>
                                        <div className="flex justify-between items-center mb-4">
                                            <label className="block">Classifications</label>
                                            <Button
                                                onClick={() => {
                                                    const selectedCategoryData = categories.find(cat => cat._id === categoryAssignment.category);
                                                    const allClassifications = selectedCategoryData?.classifications?.map(classification => ({
                                                        id: classification._id,
                                                        phase: null,
                                                        groups: []
                                                    })) || [];

                                                    const newValue = [...value];
                                                    newValue[categoryIndex] = {
                                                        ...newValue[categoryIndex],
                                                        classifications: allClassifications
                                                    };
                                                    onChange(newValue);
                                                }}
                                                variant="outlined"
                                                size="small"
                                                className="text-sm"
                                            >
                                                Add All Classifications
                                            </Button>
                                        </div>

                                        {categoryAssignment.classifications.map((classification, classIndex) => (
                                            <div key={classIndex} className="mb-4 p-4 border rounded">
                                                <div className="grid grid-cols-1 gap-4">
                                                    {/* Classification Select */}
                                                    <div>
                                                        <Select
                                                            value={
                                                                categories
                                                                    .find(cat => cat._id === (categoryAssignment.category?._id || categoryAssignment.category))
                                                                    ?.classifications
                                                                    ?.find(c => c._id === (classification.id?._id || classification.id))
                                                            }
                                                            onChange={(selectedClassification) => {
                                                                const newValue = [...value];
                                                                newValue[categoryIndex].classifications[classIndex] = {
                                                                    ...classification,
                                                                    id: selectedClassification._id
                                                                };
                                                                onChange(newValue);
                                                            }}
                                                            options={
                                                                categories
                                                                    .find(cat => cat._id === (categoryAssignment.category?._id || categoryAssignment.category))
                                                                    ?.classifications || []
                                                            }
                                                            getOptionLabel={(option) => option.name}
                                                            getOptionValue={(option) => option._id}
                                                        />
                                                    </div>

                                                    {/* Phase Select */}
                                                    <div>
                                                        <Select
                                                            value={
                                                                classification.phase
                                                                    ? { value: classification.phase, label: `Phase ${classification.phase}` }
                                                                    : null
                                                            }
                                                            onChange={(selectedPhase) => {
                                                                const newValue = [...value];
                                                                newValue[categoryIndex].classifications[classIndex] = {
                                                                    ...classification,
                                                                    phase: selectedPhase?.value || null
                                                                };
                                                                onChange(newValue);
                                                            }}
                                                            options={[
                                                                { value: 1, label: 'Phase 1' },
                                                                { value: 2, label: 'Phase 2' }
                                                            ]}
                                                            isClearable
                                                            placeholder="Select Phase"
                                                        />
                                                        <div className="text-sm text-gray-500 mt-1">
                                                            Leave empty if no phases are required
                                                        </div>
                                                    </div>

                                                    {/* Group Assignment */}
                                                    <div>
                                                        <div className="flex justify-between items-center mb-2">
                                                            <label className="block">Assign Specific Groups</label>
                                                            {classification.groups && classification.groups.length > 0 && (
                                                                <Button
                                                                    onClick={() => {
                                                                        const newValue = [...value];
                                                                        newValue[categoryIndex].classifications[classIndex] = {
                                                                            ...classification,
                                                                            groups: []
                                                                        };
                                                                        onChange(newValue);
                                                                    }}
                                                                    variant="outlined"
                                                                    size="small"
                                                                    color="error"
                                                                    className="text-sm"
                                                                >
                                                                    Clear All
                                                                </Button>
                                                            )}
                                                        </div>
                                                        {(() => {
                                                            const key = getClassificationKey(categoryAssignment.category, classification.id);
                                                            const availableGroups = groupsByClassification[key] || [];
                                                            const isLoading = loadingStates[key] || false;
                                                            const currentSelection = availableGroups.filter(group =>
                                                                classification.groups?.some(g =>
                                                                    (g._id || g) === group._id
                                                                )
                                                            );

                                                            const handleOptionClick = (selectedOption, event, classificationKey) => {
                                                                const newSelection = handleGroupSelection(
                                                                    selectedOption,
                                                                    currentSelection,
                                                                    availableGroups,
                                                                    classificationKey,
                                                                    event
                                                                );

                                                                const newValue = [...value];
                                                                newValue[categoryIndex].classifications[classIndex] = {
                                                                    ...classification,
                                                                    groups: newSelection.map(group => group._id)
                                                                };
                                                                onChange(newValue);
                                                            };

                                                            return (
                                                                <Select
                                                                    isMulti
                                                                    isLoading={isLoading}
                                                                    value={currentSelection}
                                                                    onChange={() => {}} // Disabled - we handle selection via custom option clicks
                                                                    options={availableGroups}
                                                                    getOptionLabel={(option) => option.name}
                                                                    getOptionValue={(option) => option._id}
                                                                    placeholder={isLoading ? "Loading groups..." : "Select groups (optional - click to select, shift+click for range)"}
                                                                    closeMenuOnSelect={false}
                                                                    hideSelectedOptions={false}
                                                                    blurInputOnSelect={false}
                                                                    components={{
                                                                        Option: CustomOption
                                                                    }}
                                                                    classificationKey={key}
                                                                    onOptionClick={handleOptionClick}
                                                                    menuIsOpen={openDropdowns[key]}
                                                                    onMenuOpen={() => setOpenDropdowns(prev => ({ ...prev, [key]: true }))}
                                                                    onMenuClose={() => setOpenDropdowns(prev => ({ ...prev, [key]: false }))}
                                                                    maxMenuHeight={200}
                                                                />
                                                            );
                                                        })()}
                                                        <div className="text-sm text-gray-500 mt-1">
                                                            Leave empty to assign all groups in this classification.<br/>
                                                            <strong>Tip:</strong> Click to select/deselect groups. Hold Shift and click to select a range of groups.
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Remove Classification Button */}
                                                <Button
                                                    onClick={() => {
                                                        const newValue = [...value];
                                                        newValue[categoryIndex].classifications =
                                                            categoryAssignment.classifications.filter((_, i) => i !== classIndex);
                                                        onChange(newValue);
                                                    }}
                                                    className="mt-2"
                                                    variant="outlined"
                                                    color="error"
                                                    size="small"
                                                >
                                                    Remove Classification
                                                </Button>
                                            </div>
                                        ))}

                                        {/* Add Classification Button */}
                                        <Button
                                            onClick={() => {
                                                const newValue = [...value];
                                                if (!newValue[categoryIndex].classifications) {
                                                    newValue[categoryIndex].classifications = [];
                                                }
                                                newValue[categoryIndex].classifications.push({
                                                    id: '',
                                                    phase: null,
                                                    groups: []
                                                });
                                                onChange(newValue);
                                            }}
                                            variant="outlined"
                                            className="mt-2"
                                        >
                                            Add Classification
                                        </Button>
                                    </div>
                                )}

                                {/* Remove Category Button */}
                                <Button
                                    onClick={() => {
                                        const newValue = value.filter((_, i) => i !== categoryIndex);
                                        onChange(newValue);
                                    }}
                                    className="mt-4"
                                    variant="outlined"
                                    color="error"
                                >
                                    Remove Category
                                </Button>
                            </div>
                        ))}

                        {/* Add Category Button */}
                        <Button
                            onClick={() => {
                                onChange([
                                    ...value,
                                    {
                                        category: '',
                                        classifications: []
                                    }
                                ]);
                            }}
                            variant="contained"
                            className="mt-4"
                            sx={{
                                backgroundColor: '#00773d', // iconGreen color
                                '&:hover': {
                                    backgroundColor: '#357e63' // darker shade for hover
                                }
                            }}
                        >
                            Add Category
                        </Button>
                    </div>
                )}
            />

            <div className="flex justify-between mt-6">
                <Button onClick={handlePrevStep} variant="outlined"
                    sx={{
                        borderColor: '#357e63',
                        color: '#357e63',
                        '&:hover': {
                            borderColor: '#357e63',
                            color: '#357e63'
                        }
                    }}>
                    Previous
                </Button>
                <Button onClick={handleNextStep} variant="contained" sx={{
                    backgroundColor: '#00773d', // iconGreen color
                    '&:hover': {
                        backgroundColor: '#357e63' // darker shade for hover
                    }
                }}>
                    Next
                </Button>
            </div>
        </div>
    );
});

export default CategoryAssignments;
