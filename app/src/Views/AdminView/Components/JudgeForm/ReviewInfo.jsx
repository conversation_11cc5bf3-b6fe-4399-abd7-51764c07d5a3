import { observer } from 'mobx-react';
import { Button } from '@mui/material';
import { useState, useEffect } from 'react';
import { fetchEntries } from '../../../../lib/api';

const ReviewInfo = observer(({
    watch,
    handlePrevStep,
    categories,
    handleSubmit,
    isEdit,
}) => {
    const name = watch('name'); // Updated from 'judgeName'
    const email = watch('email'); // Updated from 'judgeEmail'
    const assigned = watch('assigned') || [];

    // State to store resolved group data
    const [groupsByClassification, setGroupsByClassification] = useState({});
    const [loadingGroups, setLoadingGroups] = useState(false);

    // Function to create a unique key for category/classification combination
    const getClassificationKey = (categoryId, classificationId) => {
        const catId = categoryId._id || categoryId;
        const classId = classificationId._id || classificationId;
        return `${catId}-${classId}`;
    };

    // Function to fetch groups for a specific category and classification
    const loadGroups = async (categoryId, classificationId) => {
        const key = getClassificationKey(categoryId, classificationId);

        // Skip if already loaded
        if (groupsByClassification[key]) {
            return;
        }

        setLoadingGroups(true);
        try {
            const currentYear = new Date().getFullYear();
            const data = await fetchEntries(categoryId._id || categoryId, classificationId._id || classificationId, currentYear, false);

            const formattedGroups = data.groups.map(group => {
                // Get the maskedName either directly or from classifications
                let maskedName;
                if (group.maskedName) {
                    maskedName = group.maskedName;
                } else if (group.classifications && group.classifications.length > 0) {
                    const targetClassificationId = classificationId._id || classificationId;

                    const matchingClassification = group.classifications.find(
                        c => {
                            const classId = c.id?._id || c.id;
                            return classId?.toString() === targetClassificationId.toString()
                        }
                    );
                    maskedName = matchingClassification?.maskedName;
                }

                // Fallback to 'Unknown' if no maskedName found
                if (!maskedName) {
                    maskedName = 'Unknown';
                }

                return {
                    _id: group._id,
                    name: `Group ${maskedName}`,
                    maskedName: maskedName
                };
            });

            setGroupsByClassification(prev => ({ ...prev, [key]: formattedGroups }));
        } catch (error) {
            console.error('Error loading groups for review:', error);
            setGroupsByClassification(prev => ({ ...prev, [key]: [] }));
        } finally {
            setLoadingGroups(false);
        }
    };

    // Load groups when component mounts or assigned data changes
    useEffect(() => {
        if (assigned && assigned.length > 0) {
            assigned.forEach((assignment) => {
                if (assignment.classifications) {
                    assignment.classifications.forEach((classification) => {
                        if (assignment.category && classification.id && classification.groups && classification.groups.length > 0) {
                            loadGroups(assignment.category, classification.id);
                        }
                    });
                }
            });
        }
    }, [assigned]);

    const getCategoryName = (categoryId) => {
        const category = categories?.find(cat => cat._id === (categoryId?._id || categoryId));
        return category ? category.name : 'Unknown Category';
    };

    const getClassificationName = (categoryId, classificationId) => {
        const category = categories?.find(cat => cat._id === (categoryId?._id || categoryId));
        const classification = category?.classifications?.find(c => c._id === (classificationId?._id || classificationId));
        return classification ? classification.name : 'Unknown Classification';
    };

    const getPhaseLabel = (phase) => {
        if (!phase) return 'No Phase';
        return `Phase ${phase}`;
    };

    const getGroupNames = (groups, categoryId, classificationId) => {
        if (!groups?.length) return 'All Groups';

        const key = getClassificationKey(categoryId, classificationId);
        const availableGroups = groupsByClassification[key] || [];

        if (availableGroups.length === 0) {
            return loadingGroups ? 'Loading groups...' : 'Groups (loading...)';
        }

        return groups.map(groupId => {
            // Find the group in our loaded data
            const group = availableGroups.find(g => g._id === (groupId._id || groupId));
            if (group) {
                return group.name; // This will be "Group {maskedName}"
            } else {
                console.warn('Group not found in loaded data:', groupId);
                return 'Unknown Group';
            }
        }).join(', ');
    };

    return (
        <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-4">Review Information</h2>
            
            {/* Basic Information */}
            <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Basic Information</h3>
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <span className="text-gray-600">Name:</span>
                        <span className="ml-2">{name}</span>
                    </div>
                    <div>
                        <span className="text-gray-600">Email:</span>
                        <span className="ml-2">{email}</span>
                    </div>
                </div>
            </div>

            {/* Category Assignments */}
            <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Category Assignments</h3>
                {assigned?.length > 0 ? (
                    assigned.map((assignment, index) => (
                        <div key={index} className="mb-4 last:mb-0">
                            <h4 className="font-medium text-lg mb-2">
                               Category: {getCategoryName(assignment.category)}
                            </h4>
                            <div className="space-y-3">
                                {assignment.classifications?.map((classification, classIndex) => (
                                    <div key={classIndex} className="bg-gray-50 p-3 rounded border">
                                        <div className="space-y-2">
                                            <div>
                                                <span className="text-gray-600">Classification:</span>
                                                <span className="ml-2">
                                                    {getClassificationName(assignment.category, classification.id)}
                                                </span>
                                            </div>
                                            <div>
                                                <span className="text-gray-600">Phase:</span>
                                                <span className="ml-2">
                                                    {getPhaseLabel(classification.phase)}
                                                </span>
                                            </div>
                                            <div>
                                                <span className="text-gray-600">Groups:</span>
                                                <span className="ml-2">
                                                    {getGroupNames(classification.groups, assignment.category, classification.id)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))
                ) : (
                    <div className="text-gray-500 italic">
                        No categories assigned
                    </div>
                )}
            </div>

            <div className="flex justify-between mt-6">
                <Button 
                    onClick={handlePrevStep}
                    variant="outlined"
                    sx={{
                        borderColor: '#357e63',
                        color: '#357e63',
                        '&:hover': {
                            borderColor: '#357e63',
                            color: '#357e63'
                        }
                    }}
                >
                    Previous
                </Button>
                <Button 
                    onClick={handleSubmit}
                    variant="contained"
                    sx={{
                        backgroundColor: '#00773d', // iconGreen color
                        '&:hover': {
                            backgroundColor: '#357e63' // darker shade for hover
                        }
                    }}
                >
                    {isEdit ? "Save Changes" : "Add Judge"}
                </Button>
            </div>
        </div>
    );
});

export default ReviewInfo;
