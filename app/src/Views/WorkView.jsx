// import { useState,useRef, useEffect } from "react";
import AudioPlayer from "../Components/WorkViewComponents/AudioPlayer";
import MemoRecorder from "../Components/WorkViewComponents/MemoRecorder";
import AudioRecorder from "../Components/WorkViewComponents/AudioRecorder";
import { useEffect, useState, useContext } from "react";
import QuestionForm from "../Components/WorkViewComponents/QuestionForms";
import classNames from "classnames";
import DebugBreadcrumb from "../Components/TestComponents/DebugBreadcrumb";
import { observer } from 'mobx-react';
import { AppProviderStore } from "../AppStore";
import { toJS } from "mobx";

function WorkView({ group }) {
  console.log('work view', toJS(group))
  const { AppStore } = useContext(AppProviderStore);
  const {currentTrack, setTrack, trackList} = AppStore;

  const initButtons =
    trackList.length > 1
      ? {
          prev: false,
          next: true,
        }
      : false;
  const [toggleButtons, setButtons] = useState(initButtons);

  // useEffect(() => {
  //   setTrackList[group.tracks];
  //   setTrack[group.tracks[0]];
  //   const initButtons =
  //     trackList.length > 1
  //       ? {
  //           prev: false,
  //           next: true,
  //         }
  //       : false;
  //   setButtons(initButtons);
  // }, [group]);
  useEffect(()=>{
    setButtons(initButtons)
  },[group])

  const changeTrack = async (direction) => {
    // Prevent navigation if recording or saving a memo
    if (AppStore.isRecording) {
      alert('Please stop recording before changing tracks');
      return;
    }

    if (AppStore.isSavingMemo) {
      alert('Please wait for the memo to finish saving before changing tracks');
      return;
    }

    console.log(direction);
    let currIndex = trackList.indexOf(currentTrack);
    let shiftIndex = currIndex + direction;
    let maxIndex = trackList.length - 1;

    let buttons = { ...toggleButtons };

    if (shiftIndex > 0) {
      buttons.prev = true;
    } else {
      buttons.prev = false;
    }

    if (shiftIndex < maxIndex) {
      buttons.next = true;
    } else {
      buttons.next = false;
    }
    console.log(trackList[shiftIndex]);
    setTrack(trackList[shiftIndex]);
    setButtons({ ...buttons });
    console.log("track changed", currentTrack);
  };

  return (
    <div>
      <DebugBreadcrumb breadcrumbs={"Components->Views->WorkView"}/>
      <AudioPlayer
        group={group}
        track={currentTrack}
        buttons={toggleButtons}
        changeTrack={changeTrack}
      />
      <div className={classNames("grid grid-cols-[1.5fr_1fr] gap-4")}>
        {<AudioRecorder memos={AppStore.memos} />}
        <div>
          <QuestionForm />
        </div>
      </div>
    </div>
  );
}

export default observer(WorkView);
