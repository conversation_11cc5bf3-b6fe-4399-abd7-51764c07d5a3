import axios from 'axios';

export const fetchEntries = async (category, classification, year, isArchived) => {
  console.log(category, classification, year, isArchived);
  try {
    const response = await axios.get('/api/groups', {
      params: {
        category,
        classification,
        year,
        isArchived
      }
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching entries:", error);
    return { groups: [], totalDuration: {} }; // Return empty data structure
  }
};