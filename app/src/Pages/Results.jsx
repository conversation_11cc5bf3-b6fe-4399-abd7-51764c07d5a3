import { useState, useEffect, useContext } from "react";
import AppHeader from "../Components/Header/Header";
import Sidebar from "../Components/Sidebar/ResultSidebar";
import ResultView from "../Views/ResultView";
import { observer } from 'mobx-react';
import { AppProviderStore } from "../AppStore";
import { get, toJS } from "mobx";
import axios from "axios";

function Results() {
  const { AppStore } = useContext(AppProviderStore);
  const [menuOptions, setMenu] = useState([]);
  const [contests, setContests] = useState([]);
  const [results, setResults] = useState([]);
  const [selectedOption, setSelected] = useState();
  const { getCompetitionState, competitionState, user } = AppStore;
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  async function fetchResults(id, contests, year) {
    if (contests === 'citation') {
      let requests = id.map(i => {
        return `/api/getWinners?categoryId=${i}&year=${year}&isCitation=true`
      });
      console.log('citation requests', requests);
      Promise.all(requests.map((request) => axios.get(request))).then(
        axios.spread((...allData) => {
          let combinedResults = {};

          const firstCallData = allData[0].data;
          const secondCallData = allData[1].data;

          for (const [awardType, classifications] of Object.entries(firstCallData)) {
            combinedResults[awardType] = [...classifications];

            if (secondCallData[awardType]) {
              combinedResults[awardType].push(...secondCallData[awardType]);
            }
          }

          for (const [awardType, classifications] of Object.entries(secondCallData)) {
            if (!combinedResults[awardType]) {
              combinedResults[awardType] = [...classifications];
            }
          }
          setResults(combinedResults);
        })
      );
      return; // Add this return statement
    }
    
    let response = await axios.get(`/api/getWinners?categoryId=${id}&year=${year}&isCitation=false`);
    setResults(response.data);
  }

  async function handleSelect(option) {
    setSelected(option);
    if (option.title === "Citation Of Excellence") {
      fetchResults(option.ids, 'citation', selectedYear); // Fetch results based on selected year
      return;
    }
    fetchResults(option._id, 'moe', selectedYear); // Fetch results based on selected year
  }

  async function handleYearChange(event) {
    const newYear = event.target.value;
    setSelectedYear(newYear);
  }

  useEffect(() => {
    if (selectedOption) {
      if (selectedOption.title === "Citation Of Excellence") {
        fetchResults(selectedOption.ids, 'citation', selectedYear);
      } else {
        fetchResults(selectedOption._id, 'moe', selectedYear);
      }
    }
  }, [selectedYear, selectedOption]);

  useEffect(() => {
    async function fetchMenuOptions() {
      try {
        const response = await axios.get('/api/contests')
        if (response.data && response.data.length > 0) {
          let options = []
          response.data.map(data => {
            let toPush
            if (data.name === 'Citation Of Excellence') {
              let ids = []
              data.categories.map(category => {
                ids.push(category._id)
              })
              toPush = {
                title: data.name,
                ids: ids
              }
              options.push(toPush)

            } else {
              data.categories.map(category => {
                toPush = {
                  title: category.name,
                  _id: category._id
                }
                options.push(toPush)
              })
            }
          })
          setMenu(options)
        }
      } catch (error) {
        console.error("Error fetching menu options: ", error)
      }
    }
    fetchMenuOptions()
  }, []);

  useEffect(() => {
    if (!competitionState) {
      getCompetitionState()
    }
  }, [competitionState]);

  const isResultStateValid = user?.isAdmin || competitionState.resultState || selectedYear < new Date().getFullYear();

  return (
    <>
      <div className="app_container">
        <div className="main_app h-full min-h-[100vh]">
          <AppHeader />
          <div className="grid grid-cols-1 md:grid-cols-[270px_1fr] pt-[90px] h-full grid-rows-1 min-h-[100vh]">
            <Sidebar
              menuOptions={menuOptions}
              handleSelect={handleSelect}
              selectedOption={selectedOption}
              selectedYear={selectedYear}
              handleYearChange={handleYearChange}
            />
            {isResultStateValid ? (
              <div className="p-5 bg-mainContent main_content overflow-auto">
                {!selectedOption && (
                  <div className="flex items-center justify-center bg-white w-full h-full">
                    <div className="bg-white rounded-2xl w-[90%] md:w-[50%] h-[50%] flex flex-col items-center justify-center shadow-[0_1px_8px_0px_rgba(170,170,170,1)]">
                      <p className="text-iconGreen font-semibold px-4 py-1 w-fit rounded-3xl border border-1 border-iconGreen">
                        {`${selectedYear} - ${competitionState.resultMessage}`}
                      </p>
                      <div className="mt-5 text-center space-y-4">
                        <div className="mb-6">
                          <p className="text-gray-700 text-lg font-medium mb-2">
                            <span className="hidden md:inline">Select a category from the sidebar to view results</span>
                            <span className="md:hidden">Select a category above to view results</span>
                          </p>
                          <p className="text-gray-500 text-sm">
                            <span className="hidden md:inline">Choose from the available categories on the left to see competition results for {selectedYear}</span>
                            <span className="md:hidden">Choose from the available categories in the menu above to see competition results for {selectedYear}</span>
                          </p>
                          <div className="mt-3 text-iconGreen">
                            <span className="hidden md:inline text-2xl">←</span>
                            <span className="md:hidden text-2xl">↑</span>
                          </div>
                        </div>
                        <div className="border-t pt-4">
                          <p className="text-gray-700 mb-3">Looking for your own results? Log in below.</p>
                          <a
                            href="/login"
                            className="p-5 w-full sm:w-1/2 bg-iconGreen text-white font-semibold rounded-3xl text-center inline-block hover:bg-iconGreenDark transition-colors duration-300"
                          >
                            Login
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {selectedOption && <ResultView contest={selectedOption} results={results} selectedYear={selectedYear} />}
              </div>
            ) : (
              <div className="flex items-center justify-center bg-white w-full h-full">
                <div className="bg-white rounded-2xl w-[90%] md:w-[50%] h-[50%] flex flex-col items-center justify-center shadow-[0_1px_8px_0px_rgba(170,170,170,1)]">
                  <p className="text-iconGreen font-semibold px-4 py-1 w-fit rounded-3xl border border-1 border-iconGreen">
                    This year's results are not live yet. Select from the drop-down to view previous years.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default observer(Results);
